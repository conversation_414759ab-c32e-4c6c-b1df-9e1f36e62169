package repository

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"time"

	"llif.org/wxcache/internal/database"
	"llif.org/wxcache/internal/spacetime"
	"llif.org/wxcache/pkg/osquery"
	"llif.org/wxcache/pkg/wxtypes"
)

type environmentRepository[T wxtypes.DataType] struct {
	*slog.Logger

	os       *database.OpenSearch
	index    database.Index
	pipeline string
}

var _ AirQualityRepository = (*environmentRepository[wxtypes.AirQualityV2])(nil)
var _ WeatherRepository = (*environmentRepository[wxtypes.WeatherV2])(nil)
var _ PollenRepository = (*environmentRepository[wxtypes.PollenV2])(nil)

func NewEnvironmentRepository[T wxtypes.DataType](l *slog.Logger, os *database.OpenSearch) *environmentRepository[T] {
	var (
		t     T
		index database.Index
	)
	switch any(t).(type) {
	case wxtypes.AirQualityV2:
		index = database.AirQualityV2
	case wxtypes.WeatherV2:
		index = database.WeatherV2
	case wxtypes.PollenV2:
		index = database.PollenV2
	default:
		panic("unknown type for database index: " + fmt.Sprintf("%T", t))
	}

	return &environmentRepository[T]{
		Logger:   l,
		os:       os,
		index:    index,
		pipeline: "split_index_env_hash_pipeline",
	}
}

// Fetch returns T data which is present within the given space time coordinates or in a radius around it.
func (r *environmentRepository[T]) Fetch(ctx context.Context, st wxtypes.SpaceTime) (result []T, err error) {
	var query = osquery.Query(osquery.Bool().Filter(
		osquery.GeoDistance("coordinates").LatLon(st.Lat, st.Long).Distance("10km"), // @TODO: allow custom distance resolution
		osquery.Range("timestamp").Gte(st.TimeFrom).Lte(st.TimeTo),
	))

	res, err := r.os.Search(ctx, r.index, query)
	if err != nil {
		return
	}
	return database.GetTypeFromResponseBody[T](ctx, r.Logger, res), nil
}

// FetchWithEmptyBuckets returns T data which is present within the given space time coordinates or in a radius around it.
// It also returns a slice of hourly buckets which represent hours inside of the given space time that do not contain any T data.
func (r *environmentRepository[T]) FetchWithEmptyBuckets(ctx context.Context, st wxtypes.SpaceTime) (result []T, emptyBuckets []wxtypes.SpaceTime, err error) {
	var (
		startTime = st.TimeFrom.Truncate(time.Hour).Format(time.RFC3339)
		endTime   = st.TimeTo.Format(time.RFC3339)

		query = osquery.Query(osquery.Bool().Filter(
			osquery.GeoDistance("coordinates").LatLon(st.Lat, st.Long).Distance("10km"), // @TODO: allow custom distance resolution
			osquery.Range("timestamp").Gte(st.TimeFrom).Lte(st.TimeTo)),
		).Aggs(
			osquery.DateHistogramAggregation("timestamp_agg", "timestamp").Interval("hour").ExtendedBounds(startTime, endTime),
		)
	)

	response, err := r.os.Search(ctx, r.index, query)
	if err != nil {
		r.ErrorContext(ctx, "failed to search for empty buckets", "err", err, "query", query.Map())
		return
	}

	result = database.GetTypeFromResponseBody[T](ctx, r.Logger, response)
	if len(result) == 0 {
		r.InfoContext(ctx, "no data in database, spreading space time input into empty hourly buckets", "space_time", st)
		emptyBuckets = append(emptyBuckets, spacetime.BucketSpaceTimeWithDelta([]wxtypes.SpaceTime{st}, time.Hour)...)
		return
	}

	buckets, ok := response.Aggregations["timestamp_agg"]
	if !ok {
		r.ErrorContext(ctx, "response does not contain timestamp aggregation", "response", response)
		return
	}

	emptyBuckets = database.GetEmptyBucketsFromAggregation(ctx, r.Logger, st.Lat, st.Long, buckets.Buckets)
	return
}

// Insert takes in a slice of type T structures and inserts them into the OpenSearch database as documents
func (r *environmentRepository[T]) Insert(ctx context.Context, data []T) error {
	if len(data) == 0 {
		return errors.New("no documents to insert")
	}

	// A slice of a type != a slice of interfaces even if the type implements the interface
	// due to the memory size and the layout
	var insertData = make([]any, 0, len(data))
	for _, d := range data {
		if err := wxtypes.ValidateStruct[T](d); err != nil {
			r.WarnContext(ctx, "could not validate structure before insert", "err", err, "struct", d)
			continue
		}
		insertData = append(insertData, d)
	}
	if len(insertData) == 0 {
		return errors.New("failed validation for all structures, nothing to insert")
	}

	r.InfoContext(ctx, "inserting documents", "count", len(insertData))
	return r.os.AddAll(ctx, r.index, insertData, r.pipeline)
}

// Delete takes in a single space time and deletes all T data which match the space time or are in a radius around it.
func (r *environmentRepository[T]) Delete(ctx context.Context, st wxtypes.SpaceTime) error {
	var query = osquery.Query(osquery.Bool().Filter(
		osquery.GeoDistance("coordinates").LatLon(st.Lat, st.Long).Distance("10km"),
		osquery.Range("timestamp").Gte(st.TimeFrom).Lte(st.TimeTo)),
	)
	res, err := r.os.DeleteByQuery(ctx, r.index, query)
	if err != nil {
		return err
	}

	r.InfoContext(ctx, "deleted documents from database", "count", res.Deleted)
	return nil
}
