package ambee

import (
	"context"
	"math/rand"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"llif.org/wxcache/pkg/testutil"
	"llif.org/wxcache/pkg/testutil/location"
	"llif.org/wxcache/pkg/testutil/testsetup"
	"llif.org/wxcache/pkg/wxtypes"
)

var (
	timeFrom  = testutil.GetISOTimeFromString("2023-07-27T20:59:04Z")
	timeTo    = testutil.GetISOTimeFromString("2023-07-27T23:59:04Z")
	locations = location.GetAll()
)

func TestAmbeeReturnDataShouldPassIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("skipping integration test")
	}

	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	var (
		a   = Init(c)
		ctx = context.Background()

		timeDayAgo = time.Now().UTC().Add(-24 * time.Hour)
		timeNow    = time.Now().UTC()
	)

	t.Run("Pollen", func(t *testing.T) {
		var spacetime = make([]wxtypes.SpaceTime, 0)

		for _, loc := range locations {
			spacetime = append(spacetime,
				wxtypes.SpaceTime{
					TimeFrom: timeDayAgo,
					TimeTo:   timeNow,
					Lat:      loc.Lat,
					Long:     loc.Long,
				},
			)
		}

		result, errBuckets := a.GetPollen(ctx, spacetime)
		require.Empty(t, errBuckets)
		require.NotEmpty(t, result)
	})

	t.Run("Pollen Forecast", func(t *testing.T) {
		timeFrom := time.Now().UTC()

		for _, loc := range locations {
			max_bucket_count := 2*24 - 1
			hours := rand.Intn(max_bucket_count) + 1
			var st = wxtypes.SpaceTime{
				Lat:      loc.Lat,
				Long:     loc.Long,
				TimeFrom: timeFrom,
				TimeTo:   timeFrom.Add(time.Duration(hours) * time.Hour),
			}

			res, err := a.GetPollenForecast(ctx, st)
			require.NoError(t, err)
			require.NotEmpty(t, res)
			require.GreaterOrEqual(t, len(res), hours)
		}
	})
}

func TestAmbeeReturnWeatherNotEmptyShouldPass(t *testing.T) {
	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	c.Server.MockHTTP = true

	var (
		a   = Init(c)
		ctx = context.Background()
	)

	res, errBuckets := a.GetWeather(
		ctx,
		[]wxtypes.SpaceTime{
			{
				Lat:      location.London.Lat,
				Long:     location.London.Long,
				TimeFrom: timeFrom,
				TimeTo:   timeTo,
			},
		},
	)
	require.Empty(t, errBuckets)
	require.NotEmpty(t, res)
}

func TestAmbeePollenCompressResultsShouldPass(t *testing.T) {
	c, err := testsetup.NewConfig()
	require.NoError(t, err)

	c.Server.MockHTTP = true

	var (
		a   = Init(c)
		ctx = context.Background()
	)

	output, errBuckets := a.GetPollen(
		ctx,
		[]wxtypes.SpaceTime{
			{
				Lat:      location.NewYork.Lat,
				Long:     location.NewYork.Long,
				TimeFrom: timeFrom,
				TimeTo:   timeTo,
			},
		},
	)
	require.Empty(t, errBuckets)
	require.NotEmpty(t, output)

	expectedOutput := []wxtypes.PollenV2{
		{
			Timestamp:        time.Date(2023, time.July, 27, 20, 0, 0, 0, time.UTC),
			Tree:             &wxtypes.PollenSpecies{Count: 0},
			Weed:             &wxtypes.PollenSpecies{Count: 10, Subspecies: []wxtypes.PollenSubspecies{{Name: "ragweed", Count: 10}}},
			Grass:            &wxtypes.PollenSpecies{Count: 4, Subspecies: []wxtypes.PollenSubspecies{{Name: "grass / poaceae", Count: 4}}},
			Coordinates:      wxtypes.Coordinates{Latitude: 40.86541360800621, Longitude: -72.8640952018731},
			Metadata:         wxtypes.Metadata{Provider: "Ambee"},
			SystemProperties: wxtypes.SystemProperties{CreatedAt: time.Date(2023, time.November, 30, 9, 52, 8, 939629000, time.UTC), Backfill: false},
		},
		{
			Timestamp:        time.Date(2023, time.July, 27, 21, 0, 0, 0, time.UTC),
			Tree:             &wxtypes.PollenSpecies{Count: 0},
			Weed:             &wxtypes.PollenSpecies{Count: 38, Subspecies: []wxtypes.PollenSubspecies{{Name: "ragweed", Count: 38}}},
			Grass:            &wxtypes.PollenSpecies{Count: 15, Subspecies: []wxtypes.PollenSubspecies{{Name: "grass / poaceae", Count: 15}}},
			Coordinates:      wxtypes.Coordinates{Latitude: 40.86541360800621, Longitude: -72.8640952018731},
			Metadata:         wxtypes.Metadata{Provider: "Ambee"},
			SystemProperties: wxtypes.SystemProperties{CreatedAt: time.Date(2023, time.November, 30, 9, 52, 8, 939647000, time.UTC), Backfill: false},
		},
		{
			Timestamp:        time.Date(2023, time.July, 27, 22, 0, 0, 0, time.UTC),
			Tree:             &wxtypes.PollenSpecies{Count: 0},
			Weed:             &wxtypes.PollenSpecies{Count: 3, Subspecies: []wxtypes.PollenSubspecies{{Name: "ragweed", Count: 3}}},
			Grass:            &wxtypes.PollenSpecies{Count: 1, Subspecies: []wxtypes.PollenSubspecies{{Name: "grass / poaceae", Count: 1}}},
			Coordinates:      wxtypes.Coordinates{Latitude: 40.86541360800621, Longitude: -72.8640952018731},
			Metadata:         wxtypes.Metadata{Provider: "Ambee"},
			SystemProperties: wxtypes.SystemProperties{CreatedAt: time.Date(2023, time.November, 30, 9, 52, 8, 939657000, time.UTC), Backfill: false},
		},
		{
			Timestamp:        time.Date(2023, time.July, 27, 23, 0, 0, 0, time.UTC),
			Tree:             &wxtypes.PollenSpecies{Count: 0},
			Weed:             &wxtypes.PollenSpecies{Count: 9, Subspecies: []wxtypes.PollenSubspecies{{Name: "ragweed", Count: 9}}},
			Grass:            &wxtypes.PollenSpecies{Count: 3, Subspecies: []wxtypes.PollenSubspecies{{Name: "grass / poaceae", Count: 3}}},
			Coordinates:      wxtypes.Coordinates{Latitude: 40.86541360800621, Longitude: -72.8640952018731},
			Metadata:         wxtypes.Metadata{Provider: "Ambee"},
			SystemProperties: wxtypes.SystemProperties{CreatedAt: time.Date(2023, time.November, 30, 9, 52, 8, 939669000, time.UTC), Backfill: false},
		},
	}

	// Override created at field in test
	for i := 0; i < len(output); i++ {
		expectedOutput[i].SystemProperties.CreatedAt = output[i].SystemProperties.CreatedAt
	}

	require.Equal(t, expectedOutput, output)

}

func TestAmbeeValidateTimeInputValidInputsShouldPass(t *testing.T) {
	var (
		a          = Ambee{}
		timeNowUtc = time.Now().UTC()
	)

	tests := []struct {
		st wxtypes.SpaceTime
	}{
		{
			st: wxtypes.SpaceTime{TimeFrom: timeNowUtc, TimeTo: timeNowUtc},
		},
		{
			st: wxtypes.SpaceTime{TimeFrom: timeNowUtc.Add(-2 * time.Minute), TimeTo: timeNowUtc.Add(-1 * time.Minute)},
		},
	}

	for _, tt := range tests {
		output, err := a.validateAmbeeTimeInput(tt.st.TimeFrom, tt.st.TimeTo)
		require.NoError(t, err)

		require.NotEmpty(t, output)
		require.Equal(t, tt.st.TimeFrom.Truncate(time.Minute), output.TimeFrom)
		require.Equal(t, tt.st.TimeTo.Truncate(time.Minute), output.TimeTo)
	}
}

func TestAmbeeValidateTimeInputFromAfterToShouldError(t *testing.T) {
	var (
		a          = Ambee{}
		timeNowUtc = time.Now().UTC()

		timeFrom = timeNowUtc.Add(-1 * time.Minute)
		timeTo   = timeNowUtc.Add(-2 * time.Minute)
	)

	res, err := a.validateAmbeeTimeInput(timeFrom, timeTo)
	require.Error(t, err)
	require.Empty(t, res)
}

func TestAmbeeValidateTimeInputPastAmbeeLimitShouldError(t *testing.T) {
	var (
		a          = Ambee{}
		timeNowUtc = time.Now().UTC()

		timeFrom = timeNowUtc.Add(-2 * AmbeeDayHistoryLimit)
		timeTo   = timeNowUtc.Add(-2 * AmbeeDayHistoryLimit)
	)

	res, err := a.validateAmbeeTimeInput(timeFrom, timeTo)
	require.Nil(t, res)
	require.Nil(t, err)
}
