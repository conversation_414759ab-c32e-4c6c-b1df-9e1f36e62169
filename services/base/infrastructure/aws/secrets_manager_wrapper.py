import json
import logging
from typing import Optional, Sequence

from botocore.exceptions import ClientError

from services.base.infrastructure.aws.service_provider import AWSServiceProvider

_manager_client = AWSServiceProvider.get_secret_manager_client()


def get_secret(secret_name: str) -> Optional[str]:
    """See https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html
    secret_name: string with name of the secret as saved on AWS
    """
    try:
        get_secret_value_response = _manager_client.get_secret_value(SecretId=secret_name)
        # Decrypts secret using the associated KMS CMK.
        # Depending on whether the secret is a string or binary, one of these fields will be populated.
        if "SecretString" in get_secret_value_response:
            secret = get_secret_value_response["SecretString"]
            return secret
        return None
    except (ClientError, Exception) as error:
        logging.critical(f"Client error occurred while fetching the secrets {repr(error)}")
        raise error


def get_secrets(secret_names: Sequence[str]) -> dict[str, dict]:
    """See https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_BatchGetSecretValue.html"""
    response = _manager_client.batch_get_secret_value(SecretIdList=secret_names)

    secrets = {}
    for secret in response.get("SecretValues", []):
        secret_name = secret.get("Name")
        secret_value = secret.get("SecretString")
        if not secret_name or not secret_value:
            logging.critical(f"Error occurred while fetching the secret {secret}")
            continue
        secrets[secret_name] = json.loads(secret_value)

    # Handle any errors
    for error in response.get("Errors", []):
        logging.critical(f"Error occurred while fetching the secrets {repr(error)}")

    return secrets
