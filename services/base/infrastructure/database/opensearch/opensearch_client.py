import logging
from typing import Dict, Optional, Sequence, Type

from opensearchpy import AsyncOpenSearch, OpenSearchException

from services.base.application.boundaries.documents import (
    GetDocumentByIdOutputBoundary,
)
from services.base.application.retry import retry
from services.base.domain.schemas.events.document_base import Document
from services.base.infrastructure.database.opensearch.opensearch_mappings import (
    DataSchemaToIndexModelMapping,
)
from settings.app_config import settings


class OpenSearchClient:
    """Intended for wrapping low level opensearch operations"""

    MAX_BULK_SIZE = settings.OS_BULK_MAX_SIZE_PER_COMMIT
    MAX_LINES_PER_COMMIT = settings.OS_BULK_MAX_SIZE_PER_COMMIT
    _client: AsyncOpenSearch

    def __init__(self, client: AsyncOpenSearch):
        self._client = client

    async def close(self):
        logging.info("Closing opensearch client.")
        await self._client.close()

    @retry(exceptions=OpenSearchException)
    async def count(self, indices: Sequence[str], body: Optional[dict] = None) -> int:
        return (await self._client.count(index=str.join(",", indices), body=body))["count"]

    @retry(exceptions=OpenSearchException)
    async def delete_by_query[T: Document](
        self,
        data_schema: Type[T] | str,
        body: Dict,
        requests_per_second: int = 1000,
        headers: Optional[Dict] = None,
        wait_for_completion: bool = True,
    ) -> Dict:
        index_name = data_schema if isinstance(data_schema, str) else DataSchemaToIndexModelMapping[data_schema].name
        response = await self._client.delete_by_query(
            body=body,
            index=index_name + "*",
            request_timeout=0,  # pyright: ignore
            conflicts="proceed",  # pyright: ignore
            requests_per_second=requests_per_second,  # pyright: ignore
            slices="auto",  # pyright: ignore
            wait_for_completion=wait_for_completion,  # pyright: ignore
            headers=headers,  # pyright: ignore
        )
        return response

    @retry(exceptions=OpenSearchException)
    async def delete_by_id(self, index_name: str, doc_id: str) -> bool:
        response: Dict = await self._client.delete(index=index_name, id=doc_id)
        op_result = response.get("result")
        if not op_result == "deleted":
            logging.exception(
                f"could not delete document with id: {doc_id} in index {index_name}, result: {op_result}."
            )
            return False
        return True

    @retry(exceptions=OpenSearchException)
    async def search_by_query[T: Document](self, data_schema: Type[T], body: Dict) -> dict:
        response = await self._client.search(
            body=body,
            index=DataSchemaToIndexModelMapping[data_schema].name + "*",
        )
        return response

    @retry(exceptions=OpenSearchException)
    async def search_indices(self, body: Dict, indices: Sequence[str]) -> dict:
        return await self._client.search(
            body=body,
            index=indices,
        )

    @retry(exceptions=OpenSearchException)
    async def insert(self, index_name: str, document: str, pipeline: str) -> dict:
        return await self._client.index(index=index_name, body=document, pipeline=pipeline)  # pyright: ignore

    @retry(exceptions=OpenSearchException)
    async def get_by_id(self, index_name: str, internal_id: str) -> GetDocumentByIdOutputBoundary:
        response = await self._client.get(index=index_name, id=internal_id)
        # TODO: check for other stuff like status code
        # https://opensearch.org/docs/latest/api-reference/document-apis/get-documents/#response-body-fields
        if not response or not response.get("_source"):
            raise OpenSearchException(f"didn't receive valid document body, got response: {response}")
        return GetDocumentByIdOutputBoundary(id=response["_id"], document=response["_source"])
