from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.query.leaf_query import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Radius<PERSON>uery, ValuesQuery
from services.base.domain.schemas.query.validators.query_validation_exception import QueryValidationException
from services.base.infrastructure.database.opensearch.opensearch_mappings import (
    DataSchemaToIndexModelMapping,
)
from services.base.infrastructure.database.opensearch.query_translator.field_refiner import FieldRefiner


class LeafQueryRefiner:
    @staticmethod
    def refine(leaf_query: LeafQuery, domain_type: type[Document]) -> LeafQuery:
        if isinstance(leaf_query, ValuesQuery):
            return LeafQueryRefiner._refine_values_query(values_query=leaf_query, domain_type=domain_type)
        elif isinstance(leaf_query, PatternQuery):
            return LeafQueryRefiner._refine_match_query(match_query=leaf_query, domain_type=domain_type)
        elif isinstance(leaf_query, RadiusQuery):
            return LeafQueryRefiner._refine_radius_query(radius=leaf_query, domain_type=domain_type)
        else:
            return leaf_query

    @staticmethod
    def _refine_match_query(match_query: PatternQuery, domain_type: type[Document]) -> PatternQuery:
        index_mapping = DataSchemaToIndexModelMapping[domain_type].mappings
        for field_name in match_query.field_names:
            field_mapping = FieldRefiner.get_field_mapping(field_name=field_name, index_mapping=index_mapping)
            if field_mapping["type"] == "keyword":
                raise QueryValidationException(f"Match Query on keyword field {field_name} is not supported.")
        return match_query

    @staticmethod
    def _refine_values_query(values_query: ValuesQuery, domain_type: type[Document]) -> ValuesQuery:
        if values_query.field_name == "_index":
            # _index queries are added by us, therefore no reason to validate them. Also, they are not part of
            # the index mapping
            return values_query

        field_name = FieldRefiner.adjust_field_name(field_name=values_query.field_name)
        refined_values_query = ValuesQuery(field_name=field_name, values=values_query.values)

        field_mapping = FieldRefiner.get_field_mapping(
            field_name=refined_values_query.field_name,
            index_mapping=DataSchemaToIndexModelMapping[domain_type].mappings,
        )

        if field_mapping["type"] == "text":
            if FieldRefiner.has_keyword_field(field_mapping=field_mapping):
                updated_field_name = refined_values_query.field_name + ".keyword"
                return ValuesQuery(field_name=updated_field_name, values=refined_values_query.values)
            else:
                raise QueryValidationException(
                    f"ValuesQuery on text field {refined_values_query.field_name} is not supported."
                )
        else:
            # No changes needed for other data types
            return refined_values_query

    @staticmethod
    def _refine_radius_query(radius: RadiusQuery, domain_type: type[Document]) -> RadiusQuery:
        index_mapping = DataSchemaToIndexModelMapping[domain_type].mappings
        field_mapping = FieldRefiner.get_field_mapping(field_name=radius.field_name, index_mapping=index_mapping)
        field_type = field_mapping["type"]
        if field_type != "geo_point":
            raise QueryValidationException(
                f"Radius query cannot be applied on field type {field_type}. Only supported type is GeoPoint."
            )

        return radius
