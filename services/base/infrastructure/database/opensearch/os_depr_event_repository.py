import json
import logging
from typing import Optional, Sequence, Type
from uuid import UUID

from services.base.application.boundaries.documents import (
    DeleteDocumentByIdOutputBoundary,
    SearchByIDDocumentsOutputBoundary,
)
from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.enums.bulk_operation import BulkOperation
from services.base.application.database.models.filter_types import <PERSON><PERSON>d<PERSON>erms<PERSON><PERSON><PERSON>, TermsFilter, UserUUIDTermsFilter
from services.base.application.database.models.filters import FilterBuilder, Filters
from services.base.application.exceptions import (
    ConflictingActionInProgress,
    InvalidDatabaseRequestException,
    PartialDataException,
)
from services.base.application.retry import retry
from services.base.application.utils.size_splitter import SizeSplitter
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.shared import IdentifiableModel, TimestampModel
from services.base.domain.schemas.shared_v2 import DeprEventModel
from services.base.infrastructure.database.opensearch.opensearch_client import OpenSearchClient
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_ID
from services.base.infrastructure.database.opensearch.opensearch_mappings import (
    DataSchemaToIndexModelMapping,
)
from services.base.infrastructure.database.opensearch.opensearch_pipeline_constants import (
    SPLIT_INDEX_CONTENT_HASH_PIPELINE,
)
from services.base.infrastructure.database.opensearch.opensearch_query_builder import OpenSearchQueryBuilder
from services.base.infrastructure.database.opensearch.opensearch_request_builder import OpenSearchRequestBuilder
from services.base.infrastructure.database.opensearch.opensearch_utils import OpenSearchUtils
from services.base.infrastructure.database.opensearch.repository.os_response_parser import OSResponseParser


class OSDeprEventRepository(DeprEventRepository):
    _client: OpenSearchClient

    def __init__(self, client: OpenSearchClient):
        self._client = client

    async def close(self):
        await self._client.close()

    async def insert[T: DeprEventModel](
        self,
        models: Sequence[T],
        bulk_type: BulkOperation = BulkOperation.Create,
        force_strong_consistency: bool = False,
    ) -> Sequence[T]:
        # Split between the individual models
        # Arrays for the input and output
        responses = []
        data_schema = type(models[0])
        index_name = DataSchemaToIndexModelMapping[data_schema].name
        refresh = "wait_for" if force_strong_consistency else "false"

        chunked_events = SizeSplitter.split(data=models)

        for chunk in chunked_events:
            insert_requests: list[dict] = []
            for event in chunk:
                insert_requests.append({bulk_type.value: {"_index": index_name}})
                insert_requests.append(event.model_dump(by_alias=True))

            response = await self._do_bulk_commit(payload=insert_requests, bulk_type=bulk_type, refresh=refresh)
            if response:
                responses.append(response)

        logging.info(f"Finished bulk_commit {len(models)} items for {data_schema.__name__}")

        # Check size of the bulk commit response
        results = await self._get_models_from_commit_responses(
            bulk_type=bulk_type, data_schema=data_schema, responses=responses
        )
        return results

    async def delete_by_id(self, doc_id: str, entry: DeprEventModel) -> DeleteDocumentByIdOutputBoundary:
        data_schema = type(entry)
        index_model = DataSchemaToIndexModelMapping[data_schema]
        index_name = OpenSearchUtils.get_index_name(
            entry=TimestampModel.map(model=entry), index_name=index_model.name, is_splittable=index_model.is_splittable
        )
        deletion_succeeded = await self._client.delete_by_id(doc_id=doc_id, index_name=index_name)

        return DeleteDocumentByIdOutputBoundary(_doc_id=entry.doc_id, deletion_succeeded=deletion_succeeded)

    async def search_by_id[T: DeprEventModel](
        self, data_schema: Type[T], doc_ids: Sequence[UUID], user_uuid: Optional[UUID]
    ) -> SearchByIDDocumentsOutputBoundary[T]:
        builder = OpenSearchQueryBuilder()
        terms_filters: list[TermsFilter] = [DocIdTermsFilter(value=[str(doc_id) for doc_id in doc_ids])]
        if user_uuid:
            terms_filters.append(UserUUIDTermsFilter(value=[str(user_uuid)]))

        builder.with_filters(filters=Filters(must_filters=FilterBuilder(terms_filters=terms_filters)))
        request_body = (
            OpenSearchRequestBuilder().with_query(query=builder._query).with_size(size=len(doc_ids))._request_body
        )
        logging.info(f"Searching document by ids {doc_ids} with request body: {request_body}")
        response = await self._client.search_by_query(data_schema=data_schema, body=request_body)
        results = OSResponseParser.get_models_from_search_response(data_schema=data_schema, response=response)
        return SearchByIDDocumentsOutputBoundary[data_schema](results=results.results)

    async def update_by_id[T: DeprEventModel](
        self,
        doc_id: str,
        original_entry: T,
        updated_entry: T,
    ) -> T:
        data_schema = type(original_entry)
        original_dict = original_entry.model_dump(by_alias=True)
        combined_document = original_dict | updated_entry.model_dump(by_alias=True)
        if combined_document == original_dict:
            raise InvalidDatabaseRequestException("Trying to update a document without any changes")

        index_model = DataSchemaToIndexModelMapping[data_schema]
        result = await self._do_update_by_id(
            original_doc_id=doc_id,
            original_index_name=OpenSearchUtils.get_index_name(
                entry=TimestampModel.map(original_entry),
                index_name=index_model.name,
                is_splittable=index_model.is_splittable,
            ),
            updated_index_name=OpenSearchUtils.get_index_name(
                entry=TimestampModel.map(updated_entry),
                index_name=index_model.name,
                is_splittable=index_model.is_splittable,
            ),
            updated_entry=data_schema(**combined_document).model_dump_json(by_alias=True),
        )
        return data_schema(**result)

    async def delete_by_query(self, user_uuid: UUID, data_schema: Type[DeprEventModel] | str, filters: Filters) -> str:
        builder = OpenSearchQueryBuilder()
        builder.with_filters(filters=filters)
        request_body = OpenSearchRequestBuilder().with_query(query=builder._query)._request_body
        index_name = data_schema if isinstance(data_schema, str) else DataSchemaToIndexModelMapping[data_schema].name
        headers = (
            {
                "X-Opaque-Id": json.dumps(
                    {
                        "name": index_name,
                        "user_uuid": str(user_uuid),
                    }
                ),
            }
            if user_uuid
            else {}
        )
        return (
            await self._client.delete_by_query(
                data_schema=data_schema, body=request_body, headers=headers, wait_for_completion=False
            )
        )["task"]

    @retry(exceptions=PartialDataException)
    async def _get_models_from_commit_responses[T](
        self,
        bulk_type: BulkOperation,
        data_schema: type[T],
        responses: Sequence[dict],
    ) -> Sequence[T]:
        # TODO:
        # 1. Check for status code {"index": {..., "status": int}}
        # HTTP 201 == new entry, 200 == entry already exists, version number > 1
        # If entry is only updated, we shouldn't return it, because it's not a new entry
        results: list[T] = []
        for response in responses:
            docs: list[dict] = []
            items = response["items"]
            for item in items:
                item = item[bulk_type.value]
                index_name = item["_index"]
                internal_id = item["_id"]
                docs.append({"_index": index_name, "_id": internal_id})

            get_output = await self._client._client.mget(body={"docs": docs})
            for doc in get_output["docs"]:
                try:
                    if not bool(doc["found"]):
                        raise PartialDataException(
                            message=f"Document with id {doc['_id']} not found in index {doc['_index']}"
                        )

                    source = doc["_source"]
                    if issubclass(data_schema, IdentifiableModel):
                        source |= {DocumentLabels.ID: source[DocumentLabels.DOC_ID]}
                    else:
                        source |= {DocumentLabels.ID: doc["_id"]}
                    results.append(data_schema(**source))
                except Exception as err:
                    logging.error(f"error {err} while parsing doc {doc}")
        return results

    async def _do_bulk_commit(self, payload: Sequence[dict], bulk_type: BulkOperation, refresh: str) -> dict:
        results = await self._client._client.bulk(
            body=payload, refresh=refresh, pipeline=SPLIT_INDEX_CONTENT_HASH_PIPELINE  # pyright: ignore
        )
        if results.get("errors"):
            for result in results.get("items"):
                if error := result.get(bulk_type.value).get("error"):
                    if error_name := error.get("type") == "version_conflict_engine_exception":
                        logging.debug(error.get("reason"))
                        continue
                    logging.error(f"Bulk Commit error {error_name}: {result}")
        return results

    async def _do_update_by_id(
        self, original_doc_id: str, original_index_name: str, updated_index_name: str, updated_entry: str
    ) -> dict:
        index_name = OpenSearchUtils.get_index_name_without_postfix(index_name=original_index_name)
        result = await self._client.insert(
            index_name=index_name,
            document=updated_entry,
            pipeline=SPLIT_INDEX_CONTENT_HASH_PIPELINE,
        )
        new_doc_id = result[OS_LABEL_ID]
        if new_doc_id == original_doc_id:
            raise InvalidDatabaseRequestException("Trying to update a document without any changes")
        logging.info(f"Indexed updated document {original_doc_id} in index {index_name} with results: {result}")

        response = await self._client.get_by_id(internal_id=new_doc_id, index_name=updated_index_name)
        document = response.document
        if not document:
            raise InvalidDatabaseRequestException("Failed to update document")

        try:
            await self._client.delete_by_id(index_name=original_index_name, doc_id=original_doc_id)
        except Exception as error:
            logging.exception(
                f"Unable to delete document with id {original_doc_id} "
                f"after creating updated doc with id {new_doc_id}, err: {error}"
            )
            # Unable to clean up the original document, so cleanup the new one
            await self._client.delete_by_id(index_name=updated_index_name, doc_id=new_doc_id)
            raise ConflictingActionInProgress("Unable to update document.")

        logging.info(f"Deleted document with doc_id: {original_doc_id} for index: {original_index_name}")
        return document
