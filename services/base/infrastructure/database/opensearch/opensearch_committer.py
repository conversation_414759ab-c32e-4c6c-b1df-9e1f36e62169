# -*- coding: utf-8 -*-
import json
import logging
from typing import Sequence

from opensearchpy import OpenSearch

from services.base.domain.enums.data_types import DataType
from services.base.infrastructure.database.opensearch.opensearch_mappings import data_type_to_index_mapping_get
from services.base.infrastructure.database.opensearch.opensearch_pipeline_constants import (
    SPLIT_INDEX_CONTENT_HASH_PIPELINE,
)
from settings.app_config import settings
from settings.app_constants import RUN_ENV_LOCAL


class OpenSearchCommitter:
    """Encapsulates custom implementation of http calls to OpenSearch"""

    @classmethod
    def bulk_commit(cls, client: OpenSearch, data_type: DataType, entries: Sequence[str]):
        """Send list of entries to ES instance using bulk insert"""

        interline = json.dumps({"create": {}})
        lines = []
        size = 0
        for entry in entries:
            if (
                size + len(entry) + len(interline) > (settings.OS_BULK_MAX_SIZE_PER_COMMIT * 0.9)
                or len(lines) == settings.OS_BULK_MAX_LINES_PER_COMMIT
            ):
                cls.commit(client=client, data_type=data_type, lines=lines)
                lines = []
                size = 0
            size += len(entry) + len(interline)
            lines.append(interline)
            lines.append(entry)

        # final flush in case we didn't end with OS_BULK_MAX_LINES_PER_COMMIT
        if len(lines) != 0:
            cls.commit(client=client, data_type=data_type, lines=lines)

        logging.info("OpenSearchCommitter: finished bulk_commit for %s", data_type)

    @staticmethod
    def commit(client: OpenSearch, data_type: DataType, lines: Sequence[str]):
        joined_data_output = "\n".join(lines) + "\n"
        ret_dict = client.bulk(
            index=data_type_to_index_mapping_get(data_type),
            body=joined_data_output,
            pipeline=SPLIT_INDEX_CONTENT_HASH_PIPELINE,  # pyright: ignore
        )
        if (settings.RUN_ENV == RUN_ENV_LOCAL) and ret_dict.get("errors"):
            logging.error(f"Error while committing: {ret_dict}")
