from typing import Any, Dict

from opensearchpy import Date, GeoPoint, Integer, Keyword, Object, Text

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.pollen import PollenFields
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    depr_get_default_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.environment_settings import (
    get_environment_common_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_CATCH_ALL
from services.base.infrastructure.database.opensearch.opensearch_index_constants import POLLEN_INDEX, OpenSearchIndex
from services.base.infrastructure.database.opensearch.opensearch_pipeline_constants import (
    ENVIRONMENT_DOC_ID_HASH_PIPELINE,
)


def get_pollen_mapping() -> Dict[str, Any]:
    return convert_dsl_mapping_to_dict(
        {
            OS_LABEL_CATCH_ALL: Text(),
            DocumentLabels.TIMESTAMP: Date(),
            PollenFields.TREE: Object(
                properties={
                    PollenFields.SPECIES_COUNT: Integer(),
                    PollenFields.SUBSPECIES: Object(
                        properties={
                            PollenFields.SUBSPECIES_NAME: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                            PollenFields.SUBSPECIES_COUNT: Integer(),
                        }
                    ),
                }
            ),
            PollenFields.WEED: Object(
                properties={
                    PollenFields.SPECIES_COUNT: Integer(),
                    PollenFields.SUBSPECIES: Object(
                        properties={
                            PollenFields.SUBSPECIES_NAME: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                            PollenFields.SUBSPECIES_COUNT: Integer(),
                        }
                    ),
                }
            ),
            PollenFields.GRASS: Object(
                properties={
                    PollenFields.SPECIES_COUNT: Integer(),
                    PollenFields.SUBSPECIES: Object(
                        properties={
                            PollenFields.SUBSPECIES_NAME: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                            PollenFields.SUBSPECIES_COUNT: Integer(),
                        }
                    ),
                }
            ),
            PollenFields.COORDINATES: GeoPoint(),
            **get_environment_common_mapping(),
        }
    )


def get_pollen_settings():
    return {"default_pipeline": None, **depr_get_default_index_settings()}


PollenIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=POLLEN_INDEX,
    mappings=get_pollen_mapping(),
    settings=get_pollen_settings(),
    is_splittable=True,
    pipeline=ENVIRONMENT_DOC_ID_HASH_PIPELINE,
)
