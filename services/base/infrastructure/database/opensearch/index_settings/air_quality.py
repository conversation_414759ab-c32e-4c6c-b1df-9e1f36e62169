from typing import Any, Dict

from opensearchpy import Date, Float, Geo<PERSON>oint, Integer, Object, Text

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.air_quality import AirQualityFields
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    depr_get_default_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.environment_settings import (
    get_environment_common_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_CATCH_ALL
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    AIR_QUALITY_INDEX,
    OpenSearchIndex,
)
from services.base.infrastructure.database.opensearch.opensearch_pipeline_constants import (
    ENVIRONMENT_DOC_ID_HASH_PIPELINE,
)


def get_air_quality_mapping() -> Dict[str, Any]:
    return convert_dsl_mapping_to_dict(
        {
            OS_LABEL_CATCH_ALL: Text(),
            DocumentLabels.TIMESTAMP: Date(),
            AirQualityFields.POLLUTANTS: Object(
                properties={
                    AirQualityFields.PM10: Float(),
                    AirQualityFields.PM25: Float(),
                    AirQualityFields.CO: Float(),
                    AirQualityFields.NO2: Float(),
                    AirQualityFields.SO2: Float(),
                    AirQualityFields.O3: Float(),
                }
            ),
            AirQualityFields.AQI: Object(
                properties={
                    AirQualityFields.EU: Integer(),
                    AirQualityFields.US: Integer(),
                    AirQualityFields.GB: Integer(),
                }
            ),
            AirQualityFields.COORDINATES: GeoPoint(),
            **get_environment_common_mapping(),
        }
    )


def get_air_quality_settings():
    return {"default_pipeline": None, **depr_get_default_index_settings()}


AirQualityIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=AIR_QUALITY_INDEX,
    mappings=get_air_quality_mapping(),
    settings=get_air_quality_settings(),
    is_splittable=True,
    pipeline=ENVIRONMENT_DOC_ID_HASH_PIPELINE,
)
