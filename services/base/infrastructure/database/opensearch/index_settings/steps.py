from typing import Any, Dict

from opensearchpy import Date, Integer, Object, Text

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.steps import StepsFields
from services.base.infrastructure.database.opensearch.index_settings.shared.common import get_common_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    depr_get_default_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.metadata import get_metadata_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.system_properties import (
    get_system_properties_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_CATCH_ALL
from services.base.infrastructure.database.opensearch.opensearch_index_constants import STEPS_INDEX, OpenSearchIndex
from services.base.infrastructure.database.opensearch.opensearch_pipeline_constants import (
    SPLIT_INDEX_CONTENT_HASH_PIPELINE,
)


def get_steps_mapping() -> Dict[str, Any]:
    return convert_dsl_mapping_to_dict(
        {
            OS_LABEL_CATCH_ALL: Text(),
            DocumentLabels.TIMESTAMP: Date(copy_to=OS_LABEL_CATCH_ALL),
            StepsFields.END_TIME: Date(copy_to=OS_LABEL_CATCH_ALL),
            StepsFields.DURATION: Integer(copy_to=OS_LABEL_CATCH_ALL),
            StepsFields.STEPS: Integer(copy_to=OS_LABEL_CATCH_ALL),
            StepsFields.STEP_DETAILS: Object(
                properties={
                    DocumentLabels.TIMESTAMP: Date(copy_to=OS_LABEL_CATCH_ALL),
                    StepsFields.END_TIME: Date(copy_to=OS_LABEL_CATCH_ALL),
                    StepsFields.DURATION: Integer(copy_to=OS_LABEL_CATCH_ALL),
                    StepsFields.STEPS: Integer(copy_to=OS_LABEL_CATCH_ALL),
                }
            ),
            **get_common_mapping(),
            **get_metadata_mapping(),
            **get_system_properties_mapping(),
        }
    )


#
# Index settings
#


def get_steps_settings() -> Dict[str, Any]:
    return {"default_pipeline": None, **depr_get_default_index_settings()}


StepsIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=STEPS_INDEX,
    mappings=get_steps_mapping(),
    settings=get_steps_settings(),
    is_splittable=True,
    pipeline=SPLIT_INDEX_CONTENT_HASH_PIPELINE,
)
