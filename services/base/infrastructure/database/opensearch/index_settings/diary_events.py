from typing import Any, Dict

from opensearchpy import <PERSON><PERSON><PERSON>, Date, Double, Float, Integer, Keyword, Object, Text

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.diary_events import (
    DiaryEventCustomDataFields,
    DiaryEventsConsumablesExtensionFields,
    Diary<PERSON>ventsFields,
    DiaryEventsPlanExtensionFields,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.common import get_common_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    depr_get_default_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.metadata import get_metadata_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.system_properties import (
    get_system_properties_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_data_labels import (
    OS_LABEL_CATCH_ALL,
)
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    DIARY_EVENTS_INDEX,
    OpenSearchIndex,
)
from services.base.infrastructure.database.opensearch.opensearch_pipeline_constants import (
    SPLIT_INDEX_CONTENT_HASH_PIPELINE,
)


def get_diary_events_mapping() -> Dict[str, Any]:
    return convert_dsl_mapping_to_dict(
        {
            OS_LABEL_CATCH_ALL: Text(),
            DocumentLabels.TIMESTAMP: Date(),
            DocumentLabels.END_TIME: Date(),
            DocumentLabels.DURATION: Float(),
            DiaryEventsFields.EXPLANATION: Text(copy_to=OS_LABEL_CATCH_ALL),
            DiaryEventsFields.TYPE: Keyword(),
            DiaryEventsFields.NAME: Text(fields={"keyword": Keyword(ignore_above=256)}, copy_to=OS_LABEL_CATCH_ALL),
            DiaryEventsFields.IS_STANDARD: Boolean(),
            DiaryEventsFields.IS_ARCHIVED: Boolean(),
            DiaryEventsFields.ORIGIN_URL: Text(copy_to=OS_LABEL_CATCH_ALL),
            DiaryEventsFields.INTENSITY: Integer(),
            DiaryEventsFields.TEMPLATE_ID: Keyword(),
            DiaryEventsFields.ASSET_REFERENCES: Object(
                properties={
                    DocumentLabels.ASSET_ID: Keyword(),
                    DocumentLabels.ASSET_TYPE: Keyword(),
                }
            ),
            DocumentLabels.TAGS: Object(
                properties={
                    DocumentLabels.TAG: Text(fields={"keyword": Keyword(ignore_above=64)}, copy_to=OS_LABEL_CATCH_ALL)
                }
            ),
            DiaryEventsFields.PLAN_EXTENSION: Object(
                properties={
                    DiaryEventsPlanExtensionFields.PLAN_ID: Keyword(),
                }
            ),
            DiaryEventsFields.CONSUMABLES_EXTENSION: Object(
                properties={
                    DiaryEventsConsumablesExtensionFields.NAME: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    DiaryEventsConsumablesExtensionFields.UNITS: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    DiaryEventsConsumablesExtensionFields.QUANTITY: Float(copy_to=OS_LABEL_CATCH_ALL),
                    DiaryEventsConsumablesExtensionFields.QUANTITY_PER_SERVING: Float(copy_to=OS_LABEL_CATCH_ALL),
                    DiaryEventsConsumablesExtensionFields.AMOUNT: Float(copy_to=OS_LABEL_CATCH_ALL),
                    DiaryEventsConsumablesExtensionFields.CALORIES: Float(copy_to=OS_LABEL_CATCH_ALL),
                    DiaryEventsConsumablesExtensionFields.FAT: Double(copy_to=OS_LABEL_CATCH_ALL),
                    DiaryEventsConsumablesExtensionFields.SATURATED_FAT: Double(copy_to=OS_LABEL_CATCH_ALL),
                    DiaryEventsConsumablesExtensionFields.POLYUNSATURATED_FAT: Double(copy_to=OS_LABEL_CATCH_ALL),
                    DiaryEventsConsumablesExtensionFields.MONOUNSATURATED_FAT: Double(copy_to=OS_LABEL_CATCH_ALL),
                    DiaryEventsConsumablesExtensionFields.TRANS_FAT: Double(copy_to=OS_LABEL_CATCH_ALL),
                    DiaryEventsConsumablesExtensionFields.CHOLESTEROL: Double(copy_to=OS_LABEL_CATCH_ALL),
                    DiaryEventsConsumablesExtensionFields.SODIUM: Double(copy_to=OS_LABEL_CATCH_ALL),
                    DiaryEventsConsumablesExtensionFields.POTASSIUM: Double(copy_to=OS_LABEL_CATCH_ALL),
                    DiaryEventsConsumablesExtensionFields.CARBOHYDRATES: Double(copy_to=OS_LABEL_CATCH_ALL),
                    DiaryEventsConsumablesExtensionFields.FIBER: Double(copy_to=OS_LABEL_CATCH_ALL),
                    DiaryEventsConsumablesExtensionFields.SUGAR: Double(copy_to=OS_LABEL_CATCH_ALL),
                    DiaryEventsConsumablesExtensionFields.PROTEIN: Double(copy_to=OS_LABEL_CATCH_ALL),
                    DiaryEventsConsumablesExtensionFields.VITAMIN_A: Double(copy_to=OS_LABEL_CATCH_ALL),
                    DiaryEventsConsumablesExtensionFields.VITAMIN_C: Double(copy_to=OS_LABEL_CATCH_ALL),
                    DiaryEventsConsumablesExtensionFields.IRON: Double(copy_to=OS_LABEL_CATCH_ALL),
                }
            ),
            DiaryEventsFields.CUSTOM_DATA: Object(
                properties={
                    DiaryEventCustomDataFields.KEY: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    DiaryEventCustomDataFields.VALUE: Float(copy_to=OS_LABEL_CATCH_ALL),
                }
            ),
            **get_common_mapping(),
            **get_metadata_mapping(),
            **get_system_properties_mapping(),
        }
    )


def get_diary_events_settings():
    return {"default_pipeline": None, **depr_get_default_index_settings()}


DiaryEventsIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=DIARY_EVENTS_INDEX,
    mappings=get_diary_events_mapping(),
    settings=get_diary_events_settings(),
    is_splittable=True,
    pipeline=SPLIT_INDEX_CONTENT_HASH_PIPELINE,
)
