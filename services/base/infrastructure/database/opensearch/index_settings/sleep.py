from typing import Any, Dict

from opensearchpy import <PERSON><PERSON><PERSON>, Date, Float, Integer, Keyword, Object, Text

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.sleep import SleepDetailFields, SleepFields, SleepSummaryFields
from services.base.infrastructure.database.opensearch.index_settings.shared.common import get_common_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    depr_get_default_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.metadata import get_metadata_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.system_properties import (
    get_system_properties_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_CATCH_ALL
from services.base.infrastructure.database.opensearch.opensearch_index_constants import SLEEP_INDEX, OpenSearchIndex
from services.base.infrastructure.database.opensearch.opensearch_pipeline_constants import (
    CONTENT_HASH_PIPELINE,
    SPLIT_INDEX_CONTENT_HASH_PIPELINE,
)


def get_sleep_mapping() -> Dict[str, Any]:
    return convert_dsl_mapping_to_dict(
        {
            OS_LABEL_CATCH_ALL: Text(),
            DocumentLabels.TIMESTAMP: Date(),
            DocumentLabels.END_TIME: Date(),
            DocumentLabels.DURATION: Integer(),
            SleepFields.SLEEP_EVENTS: Object(
                properties={
                    DocumentLabels.TIMESTAMP: Date(),
                    DocumentLabels.END_TIME: Date(),
                    DocumentLabels.DURATION: Integer(),
                    SleepFields.SLEEP_DETAIL: Object(
                        properties={
                            SleepDetailFields.STAGE: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                            DocumentLabels.TIMESTAMP: Date(copy_to=OS_LABEL_CATCH_ALL),
                            DocumentLabels.END_TIME: Date(copy_to=OS_LABEL_CATCH_ALL),
                            DocumentLabels.DURATION: Integer(copy_to=OS_LABEL_CATCH_ALL),
                        }
                    ),
                    SleepFields.SLEEP_SUMMARY: Object(
                        properties={
                            SleepSummaryFields.IS_MAIN_SLEEP: Boolean(copy_to=OS_LABEL_CATCH_ALL),
                            SleepSummaryFields.EFFICIENCY: Float(copy_to=OS_LABEL_CATCH_ALL),
                            SleepSummaryFields.EVENTS_COUNT: Integer(copy_to=OS_LABEL_CATCH_ALL),
                            SleepSummaryFields.IN_BED_SECONDS: Integer(copy_to=OS_LABEL_CATCH_ALL),
                            SleepSummaryFields.FALL_ASLEEP_SECONDS: Integer(copy_to=OS_LABEL_CATCH_ALL),
                            SleepSummaryFields.ASLEEP_SECONDS: Integer(copy_to=OS_LABEL_CATCH_ALL),
                            SleepSummaryFields.AWAKE_SECONDS: Integer(copy_to=OS_LABEL_CATCH_ALL),
                            SleepSummaryFields.AFTER_WAKEUP_SECONDS: Integer(copy_to=OS_LABEL_CATCH_ALL),
                            SleepSummaryFields.DEEP_SECONDS: Integer(copy_to=OS_LABEL_CATCH_ALL),
                            SleepSummaryFields.LIGHT_SECONDS: Integer(copy_to=OS_LABEL_CATCH_ALL),
                            SleepSummaryFields.REM_SECONDS: Integer(copy_to=OS_LABEL_CATCH_ALL),
                            SleepSummaryFields.RESTLESS_SECONDS: Integer(copy_to=OS_LABEL_CATCH_ALL),
                        }
                    ),
                }
            ),
            **get_common_mapping(),
            **get_metadata_mapping(),
            **get_system_properties_mapping(),
        }
    )


def get_sleep_settings() -> Dict[str, Any]:
    return {"default_pipeline": CONTENT_HASH_PIPELINE, **depr_get_default_index_settings()}


SleepIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=SLEEP_INDEX,
    mappings=get_sleep_mapping(),
    settings=get_sleep_settings(),
    is_splittable=True,
    pipeline=SPLIT_INDEX_CONTENT_HASH_PIPELINE,
)
