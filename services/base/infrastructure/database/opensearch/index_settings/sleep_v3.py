from typing import Any, Dict

from opensearchpy import Integer

from services.base.domain.schemas.events.sleep_v3 import SleepV3Fields
from services.base.infrastructure.database.opensearch.index_settings.shared.common import (
    get_base_event_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    get_default_event_index_rollover_conditions,
    get_default_event_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    SLEEP_V3_INDEX,
    OpenSearchIndex,
)


def get_sleep_v3_mapping() -> Dict[str, Any]:
    sleep_event_mapping = {
        SleepV3Fields.DEEP_SECONDS: Integer(),
        SleepV3Fields.LIGHT_SECONDS: Integer(),
        SleepV3Fields.REM_SECONDS: Integer(),
        SleepV3Fields.AWAKE_SECONDS: Integer(),
        SleepV3Fields.RESTLESS_MOMENTS: Integer(),
        SleepV3Fields.PROVIDER_SCORE: Integer(),
        SleepV3Fields.LLIF_SCORE: Integer(),
        SleepV3Fields.RATING: Integer(),
    }
    return convert_dsl_mapping_to_dict(sleep_event_mapping | get_base_event_mapping(), strict_mapping=True)


def get_sleep_v3_settings():
    return {
        "default_pipeline": None,
        **get_default_event_index_settings(),
        "plugins.index_state_management.rollover_alias": SLEEP_V3_INDEX,
    }


SleepV3IndexModel: OpenSearchIndex = OpenSearchIndex(
    name=SLEEP_V3_INDEX,
    mappings=get_sleep_v3_mapping(),
    settings=get_sleep_v3_settings(),
    rollover_conditions=get_default_event_index_rollover_conditions(),
    aliases=[SLEEP_V3_INDEX],
)
