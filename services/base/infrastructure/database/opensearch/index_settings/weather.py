from typing import Any, Dict

from opensearchpy import Date, Float, GeoPoint, Integer, Object, Text

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.weather import WeatherFields
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    depr_get_default_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.environment_settings import (
    get_environment_common_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_CATCH_ALL
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    WEATHER_INDEX,
    OpenSearchIndex,
)
from services.base.infrastructure.database.opensearch.opensearch_pipeline_constants import (
    ENVIRONMENT_DOC_ID_HASH_PIPELINE,
)


def get_weather_mapping() -> Dict[str, Any]:
    return convert_dsl_mapping_to_dict(
        {
            OS_LABEL_CATCH_ALL: Text(),
            DocumentLabels.TIMESTAMP: Date(),
            WeatherFields.TEMPERATURE: Object(
                properties={
                    WeatherFields.TEMPERATURE: Float(),
                    WeatherFields.FEELS_LIKE: Float(),
                }
            ),
            WeatherFields.WIND: Object(
                properties={
                    WeatherFields.SPEED: Float(),
                    WeatherFields.GUST: Float(),
                    WeatherFields.DEGREE: Float(),
                    WeatherFields.DIRECTION: Text(),
                }
            ),
            WeatherFields.HUMIDITY: Float(),
            WeatherFields.CLOUD_COVER: Integer(),
            WeatherFields.UV: Integer(),
            WeatherFields.PRESSURE: Float(),
            WeatherFields.VISIBILITY: Float(),
            WeatherFields.PRECIPITATION: Float(),
            WeatherFields.COORDINATES: GeoPoint(),
            **get_environment_common_mapping(),
        }
    )


def get_weather_settings():
    return {"default_pipeline": None, **depr_get_default_index_settings()}


WeatherIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=WEATHER_INDEX,
    mappings=get_weather_mapping(),
    settings=get_weather_settings(),
    is_splittable=True,
    pipeline=ENVIRONMENT_DOC_ID_HASH_PIPELINE,
)
