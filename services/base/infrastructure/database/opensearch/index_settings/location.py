from typing import Any, Dict

from opensearchpy import Date, Float, Geo<PERSON>oint, Integer, Keyword, Object, Text

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.location import (
    LocationFields,
    PlaceVisitDetailsFields,
    WaypointDetailsFields,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.common import get_common_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    depr_get_default_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.metadata import get_metadata_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.system_properties import (
    get_system_properties_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_data_labels import (
    OS_LABEL_CATCH_ALL,
)
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    LOCATION_HISTORY_INDEX,
    OpenSearchIndex,
)


def get_location_mapping() -> Dict[str, Any]:
    return convert_dsl_mapping_to_dict(
        {
            OS_LABEL_CATCH_ALL: Text(),
            DocumentLabels.USER_UUID: Keyword(),
            DocumentLabels.TIMESTAMP: Date(),
            DocumentLabels.END_TIME: Date(),
            DocumentLabels.DURATION: Float(),
            DocumentLabels.DISTANCE: Float(),
            LocationFields.AVERAGE_COORDINATES: GeoPoint(),
            LocationFields.START_COORDINATES: GeoPoint(),
            LocationFields.END_COORDINATES: GeoPoint(),
            LocationFields.START_ALTITUDE: Float(),
            LocationFields.END_ALTITUDE: Float(),
            LocationFields.AVERAGE_ALTITUDE: Float(),
            LocationFields.ACTIVITY_TYPE: Keyword(),
            LocationFields.ACTIVITY_TYPE_PROBABILITY: Float(),
            LocationFields.WAYPOINT_DETAILS: Object(
                properties={
                    DocumentLabels.TIMESTAMP: Date(),
                    DocumentLabels.END_TIME: Date(),
                    DocumentLabels.DURATION: Float(),
                    WaypointDetailsFields.ALTITUDE: Integer(copy_to=OS_LABEL_CATCH_ALL),
                    WaypointDetailsFields.VELOCITY: Integer(copy_to=OS_LABEL_CATCH_ALL),
                    WaypointDetailsFields.HORIZONTAL_ACCURACY: Integer(copy_to=OS_LABEL_CATCH_ALL),
                    WaypointDetailsFields.VERTICAL_ACCURACY: Integer(copy_to=OS_LABEL_CATCH_ALL),
                    WaypointDetailsFields.HEADING: Integer(copy_to=OS_LABEL_CATCH_ALL),
                    WaypointDetailsFields.SOURCE: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    WaypointDetailsFields.PLATFORM: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    DocumentLabels.COORDINATES: GeoPoint(),
                }
            ),
            LocationFields.PLACE_VISIT_DETAILS: Object(
                properties={
                    DocumentLabels.TIMESTAMP: Date(),
                    DocumentLabels.END_TIME: Date(),
                    DocumentLabels.DURATION: Float(),
                    DocumentLabels.COORDINATES: GeoPoint(),
                    PlaceVisitDetailsFields.ADDRESS: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    PlaceVisitDetailsFields.NAME: Keyword(copy_to=OS_LABEL_CATCH_ALL),
                    DocumentLabels.CONFIDENCE: Float(copy_to=OS_LABEL_CATCH_ALL),
                }
            ),
            **get_common_mapping(),
            **get_metadata_mapping(),
            **get_system_properties_mapping(),
        }
    )


def get_location_settings() -> Dict[str, Any]:
    return {"default_pipeline": None, **depr_get_default_index_settings()}


LocationIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=LOCATION_HISTORY_INDEX, mappings=get_location_mapping(), settings=get_location_settings(), is_splittable=True
)
