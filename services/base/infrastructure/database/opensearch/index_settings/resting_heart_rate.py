from typing import Any, Dict

from opensearchpy import Date, Float, Keyword, Object, Text

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.resting_heart_rate import (
    RestingHeartRateBpmDetailFields,
    RestingHeartRateFields,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.common import get_common_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    depr_get_default_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.metadata import get_metadata_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.system_properties import (
    get_system_properties_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_CATCH_ALL
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    RESTING_HEART_RATE_INDEX,
    OpenSearchIndex,
)
from services.base.infrastructure.database.opensearch.opensearch_pipeline_constants import (
    SPLIT_INDEX_CONTENT_HASH_PIPELINE,
)


def get_resting_heart_rate_mapping() -> Dict[str, Any]:
    return convert_dsl_mapping_to_dict(
        {
            OS_LABEL_CATCH_ALL: Text(),
            DocumentLabels.USER_UUID: Keyword(),
            DocumentLabels.TIMESTAMP: Date(),
            DocumentLabels.END_TIME: Date(),
            DocumentLabels.DURATION: Float(),
            RestingHeartRateFields.BPM_AVG: Float(),
            RestingHeartRateFields.BPM_MIN: Float(),
            RestingHeartRateFields.BPM_MAX: Float(),
            RestingHeartRateFields.RHR_DETAIL: Object(
                properties={
                    RestingHeartRateBpmDetailFields.TIMESTAMP: Date(copy_to=OS_LABEL_CATCH_ALL),
                    RestingHeartRateBpmDetailFields.VALUE: Float(copy_to=OS_LABEL_CATCH_ALL),
                    RestingHeartRateBpmDetailFields.CONFIDENCE: Float(copy_to=OS_LABEL_CATCH_ALL),
                }
            ),
            **get_common_mapping(),
            **get_metadata_mapping(),
            **get_system_properties_mapping(),
        }
    )


def get_resting_heart_rate_settings() -> Dict[str, Any]:
    return {"default_pipeline": None, **depr_get_default_index_settings()}


RestingHeartRateIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=RESTING_HEART_RATE_INDEX,
    mappings=get_resting_heart_rate_mapping(),
    settings=get_resting_heart_rate_settings(),
    is_splittable=True,
    pipeline=SPLIT_INDEX_CONTENT_HASH_PIPELINE,
)
