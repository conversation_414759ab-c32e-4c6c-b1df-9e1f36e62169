from typing import Any, Dict

from opensearchpy import Integer

from services.base.domain.schemas.events.feeling.emotion import EmotionFields
from services.base.infrastructure.database.opensearch.index_settings.shared.common import (
    get_base_event_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    get_default_event_index_rollover_conditions,
    get_default_event_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    FEELING_INDEX,
    OpenSearchIndex,
)


def feeling_mapping() -> Dict[str, Any]:
    feeling_mapping = {
        EmotionFields.RATING: Integer(),
    }
    return convert_dsl_mapping_to_dict(custom_mapping=feeling_mapping | get_base_event_mapping(), strict_mapping=True)


def get_event_settings():
    return {
        "default_pipeline": None,
        **get_default_event_index_settings(),
        "plugins.index_state_management.rollover_alias": FEELING_INDEX,
    }


FeelingIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=FEELING_INDEX,
    mappings=feeling_mapping(),
    settings=get_event_settings(),
    rollover_conditions=get_default_event_index_rollover_conditions(),
    aliases=[FEELING_INDEX],
)
