from typing import Sequence

from services.base.domain.schemas.shared import BaseDataModel


class OpenSearchIndex(BaseDataModel):
    name: str
    mappings: dict
    is_splittable: bool = False
    rollover_conditions: dict | None = None
    settings: dict
    analyzers: dict | None = None
    boost: float | None = None
    aliases: Sequence[str] | None = None
    pipeline: str | None = None


ALIAS_FIRST_INDEX_POINTER = "000000"
ALIAS_LAST_INDEX_POINTER = "000255"
EVENT_PREFIX = "event_"
RECORD_PREFIX = "record_"
#
# Index names
#
# EventsV3

BODY_METRIC_INDEX = EVENT_PREFIX + "body_metric"
CONTENT_INDEX = EVENT_PREFIX + "content"
ACTIVITY_INDEX = EVENT_PREFIX + "activity"
SLEEP_V3_INDEX = EVENT_PREFIX + "sleep"
EXERCISE_INDEX = EVENT_PREFIX + "exercise"
FEELING_INDEX = EVENT_PREFIX + "feeling"
NOTE_INDEX = EVENT_PREFIX + "note"
NUTRITION_INDEX = EVENT_PREFIX + "nutrition"
SYMPTOM_INDEX = EVENT_PREFIX + "symptom"
MEDICATION_INDEX = EVENT_PREFIX + "medication"
PERSON_INDEX = EVENT_PREFIX + "person"
EVENT_GROUP_INDEX = EVENT_PREFIX + "group"
# Records
SLEEP_RECORD_INDEX = RECORD_PREFIX + "sleep"

DIGIT_TO_COLLECTION_INDICES_MAP = {
    0: ACTIVITY_INDEX,
    1: BODY_METRIC_INDEX,
    2: CONTENT_INDEX,
    3: SLEEP_V3_INDEX,
    4: EXERCISE_INDEX,
    5: FEELING_INDEX,
    6: NOTE_INDEX,
    7: NUTRITION_INDEX,
    8: SYMPTOM_INDEX,
    9: MEDICATION_INDEX,
    10: PERSON_INDEX,
    11: EVENT_GROUP_INDEX,
    12: SLEEP_RECORD_INDEX,
    # Add more as needed
}

COLLECTION_INDICES_TO_DIGIT_MAP = {v: k for k, v in DIGIT_TO_COLLECTION_INDICES_MAP.items()}

# Documents
TEMPLATE_INDEX = "template"
PLAN_INDEX = "plan"
USE_CASE_INDEX = "use_case"
CONTACT_INDEX = "contact"

# EventsV2
HEART_RATE_INDEX = "heart_rate"
RESTING_HEART_RATE_INDEX = "resting_heart_rate"
SLEEP_INDEX = "sleep"
STEPS_INDEX = "steps"
DIARY_EVENTS_INDEX = "diary_events"
LOCATION_HISTORY_INDEX = "location_history"
SHOPPING_ACTIVITY_INDEX = "shopping_activity"

# Other
EXTENSION_OUTPUT_INDEX = "extension_output"
EXTENSION_INDEX = "extension_results"
INBOX_MESSAGE_INDEX = "inbox_message"
TEMP_PLAN_INDEX = "temp_plan"
USAGE_STATISTICS_INDEX = "usage_statistics"
USER_LOGS_INDEX = "user_logs"


# ENVIRONMENT V2
AIR_QUALITY_INDEX = "wx_air_quality"
WEATHER_INDEX = "wx_weather"
POLLEN_INDEX = "wx_pollen"
