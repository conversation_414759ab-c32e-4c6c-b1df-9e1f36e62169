import asyncio
from typing import Sequence

from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.models.sorts import CommonSorts
from services.base.dependency_bootstrapper import DependencyBootstrapper
from services.base.domain.enums.diary_events.diary_events_type import DiaryEventType
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.schemas.diary_events import DiaryEvents, DiaryEventsCustomDataItem
from services.base.domain.schemas.events.exercise.cardio import CardioFields
from services.base.domain.schemas.events.exercise.strength import StrengthFields
from services.base.domain.schemas.query.boolean_query import AndQuery
from services.base.domain.schemas.query.leaf_query import ExistsQuery, ValuesQuery
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.base.infrastructure.database.opensearch.migrations.scripts.convertor.convertor_helpers import (
    Convertor<PERSON>elpers,
)

if __name__ == "__main__":

    def _get_custom_data_by_name(key: str, exercise: DiaryEvents) -> DiaryEventsCustomDataItem:
        assert exercise.custom_data is not None
        for custom_data in exercise.custom_data:
            if custom_data.key == key:
                return custom_data

        raise ShouldNotReachHereException("Unexpected path")

    async def update_exercises(exercises: Sequence[DiaryEvents], depr_event_repo: DeprEventRepository, dry_run: bool):
        for updated_exercise in exercises:
            result = await depr_event_repo.search_by_id(
                data_schema=DiaryEvents, doc_ids=[updated_exercise.doc_id], user_uuid=None
            )
            assert len(result.results) == 1
            result_item = result.results[0]
            original_exercise = result_item.document
            if not dry_run:
                await depr_event_repo.update_by_id(
                    doc_id=result_item.id, original_entry=original_exercise, updated_entry=updated_exercise
                )
        if dry_run:
            print(f"Dry run would update {len(exercises)} exercises")
        else:
            print(f"Updated {len(exercises)} exercises")

    async def _get_exercises(search_service: DocumentSearchService) -> Sequence[DiaryEvents]:
        type_query = ValuesQuery(field_name="type", values=[DiaryEventType.EXERCISE])
        q = SingleDocumentTypeQuery[DiaryEvents](
            domain_type=DiaryEvents, query=AndQuery(queries=[type_query])
        )
        search_result = await search_service.search_documents_by_single_query(
            sorts=[CommonSorts.internal_id()], query=q, size=10000
        )
        return search_result.documents

    async def remove_unsupported_custom_data_combinations(
        search_service: DocumentSearchService, depr_event_repo: DeprEventRepository, dry_run: bool
    ):
        exercises = await _get_exercises(search_service)

        exercises_to_update = []

        for exercise in exercises:
            assert exercise.custom_data
            distance = ConvertorHelpers.get_custom_value(exercise, CardioFields.DISTANCE)
            elevation = ConvertorHelpers.get_custom_value(exercise, CardioFields.ELEVATION)
            count = ConvertorHelpers.get_custom_value(exercise, StrengthFields.COUNT)
            weight = ConvertorHelpers.get_custom_value(exercise, StrengthFields.WEIGHT)

            assert distance != 0
            assert elevation != 0
            assert count != 0
            assert weight != 0

            modified = False
            assert exercise.custom_data is not None

            # distance = 10, weight = 5, count = None
            if distance is not None and weight is not None and count is None:
                exercise.custom_data.remove(_get_custom_data_by_name(key=StrengthFields.WEIGHT, exercise=exercise))
                modified = True

            # distance = 10, weight = None, count = 5
            if distance is not None and count is not None and weight is None:
                exercise.custom_data.remove(_get_custom_data_by_name(key=StrengthFields.COUNT, exercise=exercise))
                modified = True

            # distance = 10, weight = 5, count = 5
            if distance is not None and weight is not None and count is not None:
                exercise.custom_data.remove(_get_custom_data_by_name(key=StrengthFields.WEIGHT, exercise=exercise))
                exercise.custom_data.remove(_get_custom_data_by_name(key=StrengthFields.COUNT, exercise=exercise))
                modified = True

            if modified:
                exercises_to_update.append(exercise)

        await update_exercises(exercises=exercises_to_update, depr_event_repo=depr_event_repo, dry_run=dry_run)

    async def remove_zero_custom_data(
        search_service: DocumentSearchService, depr_event_repo: DeprEventRepository, dry_run: bool
    ):
        def _gather_exercies(exercises: Sequence[DiaryEvents]):
            exercises_to_update = []
            for exercise in exercises:
                distance = ConvertorHelpers.get_custom_value(exercise, CardioFields.DISTANCE)
                elevation = ConvertorHelpers.get_custom_value(exercise, CardioFields.ELEVATION)
                count = ConvertorHelpers.get_custom_value(exercise, StrengthFields.COUNT)
                weight = ConvertorHelpers.get_custom_value(exercise, StrengthFields.WEIGHT)

                modified = False
                assert exercise.custom_data is not None
                if distance == 0:
                    exercise.custom_data.remove(_get_custom_data_by_name(key=CardioFields.DISTANCE, exercise=exercise))
                    modified = True
                if elevation == 0:
                    exercise.custom_data.remove(_get_custom_data_by_name(key=CardioFields.ELEVATION, exercise=exercise))
                    modified = True
                if count == 0:
                    exercise.custom_data.remove(_get_custom_data_by_name(key=StrengthFields.COUNT, exercise=exercise))
                    modified = True
                if weight == 0:
                    exercise.custom_data.remove(_get_custom_data_by_name(key=StrengthFields.WEIGHT, exercise=exercise))
                    modified = True

                if modified:
                    if len(exercise.custom_data) == 0:
                        exercise.custom_data = None
                    exercises_to_update.append(exercise)
            return exercises_to_update

        exercises = await _get_exercises(search_service)
        await update_exercises(_gather_exercies(exercises), depr_event_repo, dry_run=dry_run)
        exercises = await _get_temp_plan_exercises(search_service)
        await update_exercises(_gather_exercies(exercises), depr_event_repo, dry_run=dry_run)

    async def migrate(dry_run: bool):
        bootstrapper = DependencyBootstrapper().build()
        search_service = bootstrapper.get(interface=DocumentSearchService)
        depr_event_repo = bootstrapper.get(interface=DeprEventRepository)

        await remove_zero_custom_data(search_service, depr_event_repo, dry_run)
        await remove_unsupported_custom_data_combinations(search_service, depr_event_repo, dry_run)

    asyncio.run(migrate(dry_run=True))
