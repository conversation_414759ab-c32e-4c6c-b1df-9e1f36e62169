import asyncio
from typing import Sequence

from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.models.sorts import CommonSorts
from services.base.dependency_bootstrapper import DependencyBootstrapper
from services.base.domain.enums.diary_events.diary_events_type import DiaryEventType
from services.base.domain.repository.temp_plan_repository import TempPlanRepository
from services.base.domain.schemas.diary_events import DiaryEvents
from services.base.domain.schemas.events.exercise.cardio import CardioFields
from services.base.domain.schemas.events.exercise.strength import StrengthFields
from services.base.domain.schemas.query.boolean_query import AndQuery
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.base.domain.schemas.temp_plan import TempPlan, TempPlanDiary<PERSON>vent
from services.base.infrastructure.database.opensearch.migrations.scripts.convertor.convertor_helpers import (
    ConvertorHelpers,
)
from services.base.infrastructure.database.opensearch.migrations.scripts.diary_events_exercise.exercise_cleanup_helpers import (
    ExerciseCleanupHelpers,
)

if __name__ == "__main__":

    async def _get_exercises(search_service: DocumentSearchService) -> Sequence[DiaryEvents]:
        type_query = ValuesQuery(field_name="type", values=[DiaryEventType.EXERCISE])
        q = SingleDocumentTypeQuery[DiaryEvents](domain_type=DiaryEvents, query=AndQuery(queries=[type_query]))
        search_result = await search_service.search_documents_by_single_query(
            sorts=[CommonSorts.internal_id()], query=q, size=10000
        )
        return search_result.documents

    async def _get_temp_plan_exercises(search_service) -> Sequence[TempPlan]:
        type_query = ValuesQuery(field_name="events.type", values=[DiaryEventType.EXERCISE])
        q = SingleDocumentTypeQuery[TempPlan](domain_type=TempPlan, query=AndQuery(queries=[type_query]))
        search_result = await search_service.search_documents_by_single_query(
            sorts=[CommonSorts.internal_id()], query=q, size=10000
        )
        return search_result.documents

    async def clean_exercise_custom_data(
        search_service: DocumentSearchService,
        depr_event_repo: DeprEventRepository,
        temp_plan_repo: TempPlanRepository,
        dry_run: bool,
    ):
        def _process_exercises(
            exercises: Sequence[DiaryEvents | TempPlanDiaryEvent],
        ) -> Sequence[DiaryEvents | TempPlanDiaryEvent]:
            exercises_to_update = []
            for exercise in exercises:
                if not exercise.custom_data:
                    continue

                distance = ConvertorHelpers.get_custom_value(exercise, CardioFields.DISTANCE)
                elevation = ConvertorHelpers.get_custom_value(exercise, CardioFields.ELEVATION)
                count = ConvertorHelpers.get_custom_value(exercise, StrengthFields.COUNT)
                weight = ConvertorHelpers.get_custom_value(exercise, StrengthFields.WEIGHT)

                modified = False

                # Remove zero values
                if distance == 0:
                    exercise.custom_data.remove(
                        ExerciseCleanupHelpers.get_custom_data_by_name(key=CardioFields.DISTANCE, exercise=exercise)
                    )
                    modified = True
                if elevation == 0:
                    exercise.custom_data.remove(
                        ExerciseCleanupHelpers.get_custom_data_by_name(key=CardioFields.ELEVATION, exercise=exercise)
                    )
                    modified = True
                if count == 0:
                    exercise.custom_data.remove(
                        ExerciseCleanupHelpers.get_custom_data_by_name(key=StrengthFields.COUNT, exercise=exercise)
                    )
                    modified = True
                if weight == 0:
                    exercise.custom_data.remove(
                        ExerciseCleanupHelpers.get_custom_data_by_name(key=StrengthFields.WEIGHT, exercise=exercise)
                    )
                    modified = True

                # After removing zeros, get updated values
                distance = ConvertorHelpers.get_custom_value(exercise, CardioFields.DISTANCE)
                _ = ConvertorHelpers.get_custom_value(exercise, CardioFields.ELEVATION)
                count = ConvertorHelpers.get_custom_value(exercise, StrengthFields.COUNT)
                weight = ConvertorHelpers.get_custom_value(exercise, StrengthFields.WEIGHT)

                # Remove unsupported combinations (cardio + strength fields)
                # distance = 10, weight = 5, count = None
                if distance is not None and weight is not None and count is None:
                    exercise.custom_data.remove(
                        ExerciseCleanupHelpers.get_custom_data_by_name(key=StrengthFields.WEIGHT, exercise=exercise)
                    )
                    modified = True

                # distance = 10, weight = None, count = 5
                if distance is not None and count is not None and weight is None:
                    exercise.custom_data.remove(
                        ExerciseCleanupHelpers.get_custom_data_by_name(key=StrengthFields.COUNT, exercise=exercise)
                    )
                    modified = True

                # distance = 10, weight = 5, count = 5
                if distance is not None and weight is not None and count is not None:
                    exercise.custom_data.remove(
                        ExerciseCleanupHelpers.get_custom_data_by_name(key=StrengthFields.WEIGHT, exercise=exercise)
                    )
                    exercise.custom_data.remove(
                        ExerciseCleanupHelpers.get_custom_data_by_name(key=StrengthFields.COUNT, exercise=exercise)
                    )
                    modified = True

                if modified:
                    # Set custom_data to None if empty after cleanup
                    if len(exercise.custom_data) == 0:
                        exercise.custom_data = None
                    exercises_to_update.append(exercise)
            return exercises_to_update

        # Process regular exercises
        exercises = await _get_exercises(search_service)
        await ExerciseCleanupHelpers.update_exercises(
            _process_exercises(exercises), depr_event_repo, dry_run=dry_run  # pyright: ignore
        )

        # Process temp plan exercises
        temp_plan_exercises = await _get_temp_plan_exercises(search_service)
        temp_exercises_to_update = _process_exercises([tp.events[0] for tp in temp_plan_exercises])
        updated_temp_plans = [
            TempPlan.map(tp, fields={"events": [tpe]}) for tp, tpe in zip(temp_plan_exercises, temp_exercises_to_update)
        ]
        await ExerciseCleanupHelpers.update_temp_plans(
            temp_plans=updated_temp_plans, temp_plan_repo=temp_plan_repo, dry_run=dry_run
        )

    async def migrate(dry_run: bool):
        bootstrapper = DependencyBootstrapper().build()
        search_service = bootstrapper.get(interface=DocumentSearchService)
        depr_event_repo = bootstrapper.get(interface=DeprEventRepository)
        temp_plan_repo = bootstrapper.get(interface=TempPlanRepository)

        await clean_exercise_custom_data(
            search_service=search_service,
            depr_event_repo=depr_event_repo,
            temp_plan_repo=temp_plan_repo,
            dry_run=dry_run,
        )

    asyncio.run(migrate(dry_run=True))
