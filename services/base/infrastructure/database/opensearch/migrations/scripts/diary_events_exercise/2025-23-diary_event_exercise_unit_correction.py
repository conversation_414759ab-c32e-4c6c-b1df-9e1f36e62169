import asyncio
import logging
from typing import Any, Sequence
from uuid import UUID

from opensearchpy import AsyncOpenSearch

from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.models.sorts import CommonSorts
from services.base.dependency_bootstrapper import Dependency<PERSON>ootstrapper
from services.base.domain.enums.diary_events.diary_events_type import DiaryEventType
from services.base.domain.enums.settings.units_system import UnitsSystem
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.schemas.diary_events import DiaryEvents
from services.base.domain.schemas.events.exercise.cardio import CardioFields
from services.base.domain.schemas.events.exercise.strength import StrengthFields
from services.base.domain.schemas.query.boolean_query import AndQuery
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.leaf_query import ExistsQuery, ValuesQuery
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.base.infrastructure.database.opensearch.migrations.scripts.convertor.convertor_helpers import (
    ConvertorHelpers,
)
from services.base.infrastructure.database.opensearch.migrations.scripts.diary_events_exercise.exercise_cleanup_helpers import (
    ExerciseCleanupHelpers,
)

logger = logging.getLogger()
logger.setLevel(logging.INFO)

if __name__ == "__main__":

    async def _get_unique_users(client: AsyncOpenSearch) -> list[dict[str, Any]]:
        query = {"bool": {"filter": [{"term": {"type": "exercise"}}, {"exists": {"field": "custom_data"}}]}}
        aggs = {"unique_user": {"terms": {"field": "metadata.user_uuid", "size": 10000}}}
        body = {"query": query, "aggs": aggs, "size": 0}

        result = await client.search(index="diary_event*", body=body)
        return result["aggregations"]["unique_user"]["buckets"]

    async def _get_users_exercises(user_uuid: UUID, search_service: DocumentSearchService) -> Sequence[DiaryEvents]:
        type_query = ValuesQuery(field_name="type", values=[DiaryEventType.EXERCISE])
        custom_data_query = ExistsQuery(field_name="custom_data")
        user_uuid_query = CommonLeafQueries.metadata_user_uuid_value_query(user_uuid)
        single_query = SingleDocumentTypeQuery[DiaryEvents](
            query=AndQuery(queries=[type_query, user_uuid_query, custom_data_query]), domain_type=DiaryEvents
        )
        result = await search_service.search_documents_by_single_query(
            query=single_query, size=10000, sorts=[CommonSorts.internal_id()]
        )

        return result.documents

    async def _adjust_exercise_values(
        exercises: Sequence[DiaryEvents], unit_system: UnitsSystem
    ) -> Sequence[DiaryEvents]:
        exercises_to_update = []
        for exercise in exercises:
            if not exercise.custom_data:
                raise ShouldNotReachHereException("Those exercises should not reach here")

            distance = ConvertorHelpers.get_custom_value(exercise, CardioFields.DISTANCE)
            elevation = ConvertorHelpers.get_custom_value(exercise, CardioFields.ELEVATION)
            count = ConvertorHelpers.get_custom_value(exercise, StrengthFields.COUNT)
            weight = ConvertorHelpers.get_custom_value(exercise, StrengthFields.WEIGHT)

            assert distance != 0
            assert elevation != 0
            assert count != 0
            assert weight != 0

            is_cardio = any(x is not None for x in [distance, elevation])
            is_strength = any(x is not None for x in [count, weight])

            if (is_cardio and is_strength) or (not is_cardio and not is_strength):
                raise ShouldNotReachHereException()

            modified = False

            if distance:
                updated_distance = ExerciseCleanupHelpers.handle_distance(distance, unit_system)
                if updated_distance != distance:
                    ExerciseCleanupHelpers.update_custom_value(
                        exercise=exercise, key=CardioFields.DISTANCE, value=updated_distance
                    )
                    modified = True
            if elevation:
                updated_elevation = ExerciseCleanupHelpers.handle_elevation(elevation, unit_system)
                if updated_elevation != elevation:
                    ExerciseCleanupHelpers.update_custom_value(
                        exercise=exercise, key=CardioFields.ELEVATION, value=updated_elevation
                    )
                    modified = True
            if weight:
                updated_weight = ExerciseCleanupHelpers.handle_weight(weight, unit_system)
                if updated_weight != weight:
                    ExerciseCleanupHelpers.update_custom_value(
                        exercise=exercise, key=StrengthFields.WEIGHT, value=updated_weight
                    )
                    modified = True

            if modified:
                exercises_to_update.append(exercise)

        return exercises_to_update

    async def _process_single_user(
        user_bucket: dict[str, Any],
        member_user_settings_repo: MemberUserSettingsRepository,
        search_service: DocumentSearchService,
        depr_event_repo: DeprEventRepository,
        dry_run: bool,
        semaphore: asyncio.Semaphore,
    ) -> None:
        """Process exercise unit corrections for a single user's diary events"""
        async with semaphore:
            try:
                user_uuid = UUID(user_bucket["key"])
                doc_count = user_bucket["doc_count"]
                logger.info(f"Processing user {user_uuid}")

                unit_system = await ExerciseCleanupHelpers.get_users_unit_system(user_uuid, member_user_settings_repo)

                # Process diary event exercises
                exercises = await _get_users_exercises(user_uuid, search_service)
                assert len(exercises) == doc_count

                exercises_to_update = await _adjust_exercise_values(exercises, unit_system)
                await ExerciseCleanupHelpers.update_exercises(
                    exercises=exercises_to_update, depr_event_repo=depr_event_repo, dry_run=dry_run
                )

                logger.info(f"Completed processing user {user_uuid} - {len(exercises_to_update)} exercises updated")

            except Exception as e:
                logger.error(f"Error processing user {user_bucket.get('key', 'unknown')}: {e}")
                raise

    async def migrate(dry_run: bool):
        bootstrapper = DependencyBootstrapper().build()
        client: AsyncOpenSearch = bootstrapper.get(interface=AsyncOpenSearch)
        member_user_settings_repo = bootstrapper.get(interface=MemberUserSettingsRepository)
        search_service = bootstrapper.get(interface=DocumentSearchService)
        depr_event_repo = bootstrapper.get(interface=DeprEventRepository)
        unique_users = await _get_unique_users(client)

        semaphore = asyncio.Semaphore(10)

        logger.info(f"Starting migration for {len(unique_users)} users with max 10 concurrent processes")
        tasks = [
            _process_single_user(
                user_bucket=user_bucket,
                member_user_settings_repo=member_user_settings_repo,
                search_service=search_service,
                depr_event_repo=depr_event_repo,
                dry_run=dry_run,
                semaphore=semaphore,
            )
            for user_bucket in unique_users
        ]

        await asyncio.gather(*tasks)

        logger.info("Migration completed successfully")

    asyncio.run(migrate(dry_run=True))
