from typing import Sequence
from uuid import UUID

from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.domain.enums.settings.units_system import UnitsSystem
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.repository.temp_plan_repository import TempPlanRepository
from services.base.domain.schemas.diary_events import DiaryEvents, DiaryEventsCustomDataItem
from services.base.domain.schemas.temp_plan import TempPlan, TempPlanDiaryEvent


class ExerciseCleanupHelpers:
    @staticmethod
    def get_custom_data_by_name(key: str, exercise: DiaryEvents | TempPlanDiaryEvent) -> DiaryEventsCustomDataItem:
        assert exercise.custom_data is not None
        for custom_data in exercise.custom_data:
            if custom_data.key == key:
                return custom_data

        raise ShouldNotReachHereException("Unexpected path")

    @staticmethod
    async def update_exercises(exercises: Sequence[DiaryEvents], depr_event_repo: DeprEventRepository, dry_run: bool):
        for updated_exercise in exercises:
            result = await depr_event_repo.search_by_id(
                data_schema=DiaryEvents, doc_ids=[updated_exercise.doc_id], user_uuid=None
            )
            assert len(result.results) == 1
            result_item = result.results[0]
            original_exercise = result_item.document
            if not dry_run:
                await depr_event_repo.update_by_id(
                    doc_id=result_item.id, original_entry=original_exercise, updated_entry=updated_exercise
                )
        if dry_run:
            print(f"Dry run would update {len(exercises)} exercises")
        else:
            print(f"Updated {len(exercises)} exercises")

    @staticmethod
    async def update_temp_plans(temp_plans: Sequence[TempPlan], temp_plan_repo: TempPlanRepository, dry_run: bool):
        if dry_run:
            print(f"Dry run would update {len(temp_plans)} temp plans")
        else:
            await temp_plan_repo.update(plans=temp_plans)
            print(f"Updated {len(temp_plans)} temp plans")

    @staticmethod
    def update_custom_value(exercise, key: str, value: float):
        """Update custom value for TempPlan events"""
        assert exercise.custom_data is not None
        for custom_data in exercise.custom_data:
            if custom_data.key == key:
                custom_data.value = value
                return None

        raise ShouldNotReachHereException()

    @staticmethod
    def handle_distance(value: float, unit_system: UnitsSystem) -> float:
        assert value > 0
        if unit_system == UnitsSystem.METRIC:
            if value <= 200:
                # assume value was logged in km, therefore convert to meters
                return value * 1000
            elif value >= 500:
                # assume value in meters, do nothing
                return value
        elif unit_system == UnitsSystem.IMPERIAL:
            if value <= 130:
                # assume value in miles, convert to meters
                return value * 1609.344
            elif value >= 1000:
                # assume value is in feet
                return value * 0.3048

        raise ShouldNotReachHereException(f"Unsupported value in distance. Value: {value}, unit_system: {unit_system}")

    @staticmethod
    def handle_elevation(value: float, unit_system: UnitsSystem) -> float:
        assert value > 0
        if unit_system == UnitsSystem.METRIC:
            if value <= 1:
                # assume value was logged in km, therefore convert to meters
                return value * 1000
            elif value >= 1000:
                # assume value in meters, do nothing
                return value
        elif unit_system == UnitsSystem.IMPERIAL:
            if value <= 3300:
                # assume value in feet
                return value * 0.3048

        raise ShouldNotReachHereException(f"Unsupported value in elevation. Value: {value}, unit_system: {unit_system}")

    @staticmethod
    def handle_weight(value: float, unit_system: UnitsSystem) -> float:
        assert value > 0
        if unit_system == UnitsSystem.METRIC:
            if value <= 250:
                return value  # weight is stored in kg
            elif value >= 1000:
                # assumed logged in grams
                return value / 1000
        elif unit_system == UnitsSystem.IMPERIAL:
            if value <= 550:
                return value * 0.45359237
        raise ShouldNotReachHereException(f"Unsupported value in weight. Value: {value}, unit_system: {unit_system}")

    @staticmethod
    async def get_users_unit_system(
        user_uuid: UUID, member_user_settings_repo: MemberUserSettingsRepository
    ) -> UnitsSystem:
        user_settings = await member_user_settings_repo.get_by_uuid(user_uuid)
        assert user_settings
        return user_settings.general.units_system
