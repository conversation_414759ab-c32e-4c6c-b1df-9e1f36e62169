"""The usage of this script is to migrate existing data to ensure all datetime objects are timezone aware."""

import asyncio
import logging

from opensearchpy import Async<PERSON>penSearch

from services.base.domain.enums.data_types import DataType
from services.base.infrastructure.database.opensearch.query_methods.insert_or_update import update_by_query_async
from services.base.infrastructure.database.opensearch.wrappers.client import get_async_default_os_client

logger = logging.getLogger()
logger.setLevel(logging.INFO)

TARGET_DATA_TYPES = [data_type for data_type in DataType]


async def run_migrations():
    client = get_async_default_os_client()
    for data_type in TARGET_DATA_TYPES:
        await run_migration(client=client, data_type=data_type)


async def run_migration(data_type: DataType, client: AsyncOpenSearch):
    await ensure_aware_datetimes(client=client, data_type=data_type)


async def ensure_aware_datetimes(client: AsyncOpenSearch, data_type: DataType):
    query = {
        "script": {
            "lang": "painless",
            "source": """
                boolean isAwareDatetime(String value) {
                    try {
                        ZonedDateTime.parse(value, DateTimeFormatter.ISO_DATE_TIME);
                        return true;    
                    } 
                    catch (Exception e) {
                        return false;
                    }
                }
                
                boolean isDatetime(String value) {
                    try {
                        LocalDateTime.parse(value, DateTimeFormatter.ISO_DATE_TIME);
                        return true;
                    } 
                    catch (Exception e) {
                        return false;
                    }
                }
      
                def updateValues(def content) {
                    if (content instanceof HashMap) {
                        for (key in content.keySet()) {
                            content[key] = updateValues(content[key]);
                        }
                    }
                    else if (content instanceof ArrayList) {
                        def newList = new ArrayList();
                        for (item in content) {
                            newList.add(updateValues(item));
                        }
                        return newList;
                    } 
                    else {
                        if (content instanceof String) {
                            if (!isAwareDatetime(content)) {
                                if (isDatetime(content)) {
                                    return LocalDateTime
                                        .parse(content, DateTimeFormatter.ISO_DATE_TIME)
                                        .atZone(ZoneId.systemDefault());
                                }
                            }
                        }
                    }
                    return content;     
                }
                
                updateValues(ctx._source);
            """,
        }
    }
    result = await update_by_query_async(
        client=client,
        data_type=data_type,
        requests_per_second=5000,
        query=query,
    )
    logging.info(f"Updated {data_type} data with result: {result}")


if __name__ == "__main__":
    asyncio.run(run_migrations())
