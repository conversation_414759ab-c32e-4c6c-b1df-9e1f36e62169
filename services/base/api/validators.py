from fastapi import Request
from pympler.asizeof import asizeof

from services.base.application.exceptions import BadRequestException
from services.base.application.size_constants import SIZE_MB


async def validate_input_size(request: Request):
    request_body = await request.body()
    request_size = asizeof(request_body)

    if request_size >= (50 * SIZE_MB):
        raise BadRequestException(
            message=f"expected size of data exceeded, request size: {request_size / 1_000_000} MB, limit: {50 * SIZE_MB} MB"
        )
