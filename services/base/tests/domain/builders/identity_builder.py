from __future__ import annotations

from typing import Self
from uuid import UUID, uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.schemas.identity import Identity, IdentityType


class IdentityBuilder:
    def __init__(self):
        self._id: UUID | None = None
        self._type: IdentityType | None = None

    def build(self) -> Identity:
        return Identity(
            id=self._id or uuid4(),
            type=self._type or PrimitiveTypesGenerator.generate_random_enum(enum_type=IdentityType),
        )

    def with_id(self, id: UUID) -> Self:
        self._id = id
        return self
