from typing import List, Sequence

from pydantic import AwareDatetime

from services.base.api.query.boolean_query_api import BooleanQueryType, CompoundBooleanQueryAPI
from services.base.api.query.leaf_query_api import LeafQueryConstants, PatternQueryAPI, RangeQueryAPI, ValuesQueryAPI
from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.metadata import Organization
from services.base.domain.enums.metadata_v3 import Origin
from services.base.domain.schemas.diary_events import DiaryEventsFields
from services.base.domain.schemas.events.document_base import EventMetadataFields
from services.base.domain.schemas.query.leaf_query import MatchType


class LeafQueryAPIHelper:

    @staticmethod
    def create_values_query(field_name: str, values: List[str]) -> ValuesQueryAPI:
        return ValuesQueryAPI(type=LeafQueryConstants.VALUES, field_name=field_name, values=values)

    @staticmethod
    def create_range_query(
        field_name: str, gte: AwareDatetime | int | None = None, lte: AwareDatetime | int | None = None
    ) -> RangeQueryAPI:
        return RangeQueryAPI(type=LeafQueryConstants.RANGE, field_name=field_name, gte=gte, lte=lte)

    @staticmethod
    def create_pattern_query(
        field_names: Sequence[str], pattern: str, match_type: MatchType = MatchType.DEFAULT
    ) -> PatternQueryAPI:
        return PatternQueryAPI(
            type=LeafQueryConstants.PATTERN, field_names=field_names, pattern=pattern, match_type=match_type
        )

    @staticmethod
    def organization_values_query(organizations: Sequence[Organization]) -> ValuesQueryAPI:
        return ValuesQueryAPI(
            field_name=f"{DocumentLabels.METADATA}.{DocumentLabels.ORGANIZATION}",
            values=[organization.value for organization in organizations],
            type=LeafQueryConstants.VALUES,
        )

    @staticmethod
    def origin_values_query(origins: Sequence[Origin]) -> ValuesQueryAPI:
        return ValuesQueryAPI(
            field_name=f"{DocumentLabels.METADATA}.{EventMetadataFields.ORIGIN}",
            values=[origin.value for origin in origins],
            type=LeafQueryConstants.VALUES,
        )

    @staticmethod
    def tag_values_query(tags: Sequence[NonEmptyStr]) -> ValuesQueryAPI:
        return ValuesQueryAPI(field_name=DocumentLabels.TAGS, values=tags, type=LeafQueryConstants.VALUES)

    @staticmethod
    def exclude_tags_values_query(tags: Sequence[NonEmptyStr]) -> CompoundBooleanQueryAPI:
        tag_values_query = LeafQueryAPIHelper.tag_values_query(tags=tags)
        return CompoundBooleanQueryAPI(type=BooleanQueryType.NOT, queries=[tag_values_query])

    @staticmethod
    def name_values_query(names: Sequence[NonEmptyStr]) -> ValuesQueryAPI:
        return ValuesQueryAPI(field_name=DiaryEventsFields.NAME, values=names, type=LeafQueryConstants.VALUES)
