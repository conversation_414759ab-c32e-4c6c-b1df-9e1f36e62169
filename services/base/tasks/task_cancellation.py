import logging

from services.base.tasks import futures_dict, futures_dict_lock
from services.base.tasks.future_wrapper import FutureWrapper


def cancel_task(future_wrapper: FutureWrapper) -> None:
    logging.info(f"Cancelling concurrent user file task, id: {future_wrapper.message.message_id}")
    future_wrapper.future.cancel()


def cancel_all_tasks() -> None:
    """Cancel all tasks running in the process pool"""
    with futures_dict_lock:
        for _, future_wrapper in futures_dict.items():  # future_id, future_wrapper
            cancel_task(future_wrapper)


def cancel_task_by_id(message_id: str) -> None:
    """Cancel running task with specific ID"""
    with futures_dict_lock:
        future_wrapper = futures_dict.get(message_id, None)
        if future_wrapper:
            cancel_task(future_wrapper)
