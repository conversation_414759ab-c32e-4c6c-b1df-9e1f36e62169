from uuid import UUID

from services.base.domain.enums.provider import SupportedDataProviders
from services.base.domain.enums.user_actions import UserActions
from services.base.domain.schemas.shared import BaseDataModel


class UploadEventModel(BaseDataModel):
    user_uuid: UUID
    timestamp: str
    user_filename: str
    filepath: str
    provider: SupportedDataProviders
    action: UserActions
    fallback_timezone: str
