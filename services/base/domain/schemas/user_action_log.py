from dataclasses import dataclass
from typing import List

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.user_actions import UserActions
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.domain.schemas.metadata import MetadataModel
from services.base.domain.schemas.shared import TimeIntervalModel


@dataclass(frozen=True)
class UserActionLogFields:
    USER_ACTION = "user_action"
    FILENAME = "filename"
    LOG_EVENTS = "log_events"
    METADATA = "metadata"


class UserActionLogIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> str:
        return DataType.UserLogs


class UserActionLog(Document, MetadataModel, TimeIntervalModel, UserActionLogIdentifier):
    action: UserActions = Field(alias=UserActionLogFields.USER_ACTION)
    filename: str = Field(alias=UserActionLogFields.FILENAME)
    events: List[str] = Field(alias=UserActionLogFields.LOG_EVENTS)
