from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import RoundedFloat
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.exercise.cardio import CardioCategory, CardioFields, CardioIdentifier
from services.base.domain.schemas.events.exercise.exercise import ExerciseCategory, ExerciseFields, ExerciseIdentifier
from services.base.domain.schemas.events.exercise.strength import StrengthCategory, StrengthFields, StrengthIdentifier
from services.base.domain.schemas.templates.payload.template_payload_base import TemplatePayloadBase


class ExerciseTemplatePayload(TemplatePayloadBase, ExerciseIdentifier):
    type: Literal[DataType.Exercise] = Field(alias=ExerciseFields.TYPE)
    category: ExerciseCategory = Field(alias=ExerciseFields.CATEGORY)
    rating: int | None = Field(
        alias=ExerciseFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )


class StrengthTemplatePayload(TemplatePayloadBase, StrengthIdentifier):
    type: Literal[DataType.Strength] = Field(alias=StrengthFields.TYPE)
    category: StrengthCategory = Field(alias=ExerciseFields.CATEGORY)
    count: int = Field(alias=StrengthFields.COUNT, ge=0, le=1000)
    weight: RoundedFloat | None = Field(alias=StrengthFields.WEIGHT, ge=0, le=500)
    rating: int | None = Field(
        alias=StrengthFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )


class CardioTemplatePayload(TemplatePayloadBase, CardioIdentifier):
    type: Literal[DataType.Cardio] = Field(alias=CardioFields.TYPE)
    category: CardioCategory = Field(alias=ExerciseFields.CATEGORY)
    distance: RoundedFloat | None = Field(alias=CardioFields.DISTANCE, ge=0, le=250_000)
    elevation: RoundedFloat | None = Field(alias=CardioFields.ELEVATION, ge=-500, le=8848)
    rating: int | None = Field(
        alias=CardioFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
