import asyncio
import logging
from datetime import datetime
from typing import Optional, Union

import ask_sdk_core.utils as ask_utils
from ask_sdk_core.dispatch_components import AbstractRequestHandler
from ask_sdk_core.handler_input import HandlerInput
from ask_sdk_model import Response
from ask_sdk_model.ui import LinkAccountCard

from services.base.application.voice.voice_service import VoiceService
from services.serverless.apps.alexa_best_life_event_log.application.depr_log_event_use_case import DeprLogEventUseCase
from services.serverless.apps.alexa_best_life_event_log.application.voice_exceptions import (
    RequestHasNoAccessTokenException,
    UserAlreadyLinkedToAnotherAccountException,
    UserNotLinkedException,
)
from services.serverless.apps.alexa_best_life_event_log.domain.intent_names import IntentName
from services.serverless.apps.alexa_best_life_event_log.domain.phrases import (
    ACCOUNT_ALREADY_LINKED_PHRASE,
    LINK_YOUR_ACCOUNT_PHRASE,
)
from services.serverless.apps.alexa_best_life_event_log.domain.request_types import RequestType
from settings.app_config import settings


class LogEventHandler(AbstractRequestHandler):
    """Handler for Lif Log Intent."""

    def __init__(
        self,
        log_event_use_case: DeprLogEventUseCase,
        voice_interaction_service: VoiceService,
    ):
        self._log_event_use_case = log_event_use_case
        self._voice_interaction_service = voice_interaction_service

    def can_handle(self, handler_input: HandlerInput) -> bool:
        logging.info("asserting if can handle event")
        is_intent_request: bool = ask_utils.is_request_type(RequestType.INTENT_REQUEST.value)(handler_input)
        is_log_event_intent: bool = ask_utils.is_intent_name(IntentName.LOG_EVENT.value)(handler_input)
        return is_intent_request and is_log_event_intent

    def handle(self, handler_input: HandlerInput) -> Union[None, Response]:
        loop = asyncio.get_event_loop()
        if loop.is_closed():
            loop = asyncio.new_event_loop()
        return loop.run_until_complete(self.handle_async(handler_input=handler_input))

    async def handle_async(self, handler_input: HandlerInput) -> Union[None, Response]:
        alexa_user_id = self._get_alexa_id(handler_input=handler_input)
        slot_value = self._get_slot_value(handler_input=handler_input, slot_name="event")
        logging.info(f"event log started with phrase: {slot_value}")

        try:
            access_token = self._get_linked_access_token(handler_input=handler_input)

            member_oauth2 = await self._voice_interaction_service.get_member_oauth_data(
                provider_id=alexa_user_id, linked_access_token=access_token
            )
        except RequestHasNoAccessTokenException:
            logging.exception("did not receive user's access token in the input")
            return handler_input.response_builder.speak(LINK_YOUR_ACCOUNT_PHRASE).set_card(LinkAccountCard()).response
        except UserNotLinkedException as error:
            logging.exception(error)
            return handler_input.response_builder.speak(LINK_YOUR_ACCOUNT_PHRASE).response
        except UserAlreadyLinkedToAnotherAccountException as error:
            logging.exception(error)
            return handler_input.response_builder.speak(ACCOUNT_ALREADY_LINKED_PHRASE).response

        # Parse the raw input and save the diary event in OpenSearch
        logging.info("processing event and metadata from the input")
        device_id = str(ask_utils.request_util.get_device_id(handler_input))
        timestamp: datetime = handler_input.request_envelope.request.timestamp

        event = await self._log_event_use_case.execute_async(
            user_id=member_oauth2.user_uuid,
            event_input=slot_value,
            device_id=device_id,
            timestamp=timestamp,
        )

        return handler_input.response_builder.speak(
            f"Logged {event.name} of type {event.type.value} to {settings.BEST_LIFE_ALEXA_LOG_NAME}."
        ).response

    def _get_alexa_id(self, handler_input: HandlerInput) -> str:
        logging.info("Validating lambda input")
        alexa_user_id = ask_utils.request_util.get_user_id(handler_input=handler_input)
        if not alexa_user_id:
            raise ValueError("handler did not receive user's alexa id")

        alexa_user_id = str(alexa_user_id)
        logging.info(f"received Alexa user ID: {alexa_user_id}")
        return alexa_user_id

    def _get_linked_access_token(self, handler_input: HandlerInput) -> str:
        logging.info("getting account linked access token")
        access_token = ask_utils.request_util.get_account_linking_access_token(handler_input=handler_input)
        if not access_token:
            raise RequestHasNoAccessTokenException("could not get user's access token from the response")
        return access_token

    def _get_slot_value(self, handler_input: HandlerInput, slot_name: str) -> Optional[str]:
        """Returns the given slot name from the raw handler_input"""
        return handler_input.request_envelope.request.intent.slots[slot_name].value
