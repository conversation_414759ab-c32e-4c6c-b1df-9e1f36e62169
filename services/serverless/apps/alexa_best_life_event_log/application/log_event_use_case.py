import logging
from datetime import datetime, timedelta
from typing import Sequence
from uuid import UUID, uuid4

from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.models.sorts import CommonSorts
from services.base.application.exceptions import RuntimeException
from services.base.domain.enums.metadata import Organization
from services.base.domain.enums.metadata_v3 import Origin, Service, SourceOS, SourceService
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.events.document_base import EventMetadata
from services.base.domain.schemas.events.event import Event, EventFields
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.leaf_query import MatchType, PatternQuery
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.domain.schemas.templates.group_template import GroupTemplate
from services.base.domain.schemas.templates.template import Template, TemplateFields
from services.base.type_resolver import TypeResolver
from services.serverless.apps.alexa_best_life_event_log.application.voice_exceptions import NoTemplatesFoundException


class LogEventUseCase:
    def __init__(
        self,
        event_repo: EventRepository,
        template_repo: TemplateRepository,
        search_service: DocumentSearchService,
    ):
        self._event_repo = event_repo
        self._template_repo = template_repo
        self._search_service = search_service

    async def execute_async(self, prompt: str, timestamp: datetime, owner_id: UUID) -> Sequence[Event]:
        found_template = await self._lookup_template(prompt=prompt, owner_id=owner_id)
        if not found_template:
            raise NoTemplatesFoundException(f"no template found for input {prompt}")

        events = await self._collect_event_input(
            template=found_template, owner_id=owner_id, submission_id=uuid4(), now=timestamp
        )
        inserted = await self._event_repo.insert(events=events)
        logging.info(f"user {owner_id} saved events {inserted}")
        return inserted

    async def _lookup_template(self, prompt: str, owner_id: UUID) -> Template | None:
        query = (
            BooleanQueryBuilder()
            .add_queries(
                queries=[
                    CommonLeafQueries.owner_id_value_query(user_uuid=owner_id),
                    PatternQuery(field_names=[TemplateFields.NAME], match_type=MatchType.FUZZY, pattern=prompt),
                ]
            )
            .build_and_query()
        )
        search_response = await self._search_service.search_documents_by_single_query(
            query=SingleDocumentTypeQuery[Template](query=query, domain_type=Template),
            sorts=[CommonSorts.score(), *CommonSorts.created_at_and_internal_id()],
            size=10,
        )
        documents = search_response.documents
        logging.info(f"Looked up events: {documents}")
        exact_matches = [e for e in documents if e.name == prompt]

        return exact_matches[0] if exact_matches else documents[0] if documents else None

    async def _collect_event_input(
        self, template: Template, now: datetime, owner_id: UUID, submission_id: UUID
    ) -> Sequence[Event]:
        events: list[Event] = []

        if isinstance(template, EventTemplate):
            templated_document = template.document
            timestamp = now
            end_time = (
                (timestamp + timedelta(seconds=templated_document.duration)) if templated_document.duration else None
            )
            domain_type = TypeResolver.get_event(type_id=templated_document.type)

            metadata: EventMetadata = EventMetadata(
                organization=Organization.LLIF,
                origin=Origin.LLIF,
                service=Service.VOICE,
                origin_device=None,  ## TODO correct?
                source_os=SourceOS.UNKNOWN,
                source_service=SourceService.AMAZON_ALEXA,
            )
            events.append(
                domain_type(
                    **templated_document.model_dump(by_alias=True) | {EventFields.METADATA: metadata},
                    timestamp=timestamp,
                    end_time=end_time,
                    template_id=template.id,
                    rbac=template.rbac,
                    id=uuid4(),
                    submission_id=submission_id,
                    asset_references=None,
                    plan_extension=None,
                    group_id=None,
                )
            )
        elif isinstance(template, GroupTemplate):
            templates = await self._template_repo.search_by_id(ids=template.template_ids)
            if not templates:
                raise RuntimeException(f"No associated templates for group template with id: {template.id}")
            for template in templates:
                event = await self._collect_event_input(
                    template=template, submission_id=submission_id, owner_id=owner_id, now=now
                )
                events.extend(event)

        return events
