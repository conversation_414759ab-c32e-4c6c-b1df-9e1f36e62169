from services.base.domain.enums.usage_statistics import ReportTimeframe
from services.base.domain.schemas.usage_statistics_output import UsageStatistics
from services.serverless.apps.usage_statistics_generator.application.usage_statistics_use_case import (
    UsageStatisticsUseCase,
)


async def test_usage_stats_use_case_should_pass(
    usage_stats_use_case: UsageStatisticsUseCase,
):
    # Simply runs the use case and asserts that the result is serialized correctly
    # Value tests are included for the sub structures
    result = await usage_stats_use_case.execute_async(report_timeframe=ReportTimeframe.WEEKLY)
    assert isinstance(result, UsageStatistics)
