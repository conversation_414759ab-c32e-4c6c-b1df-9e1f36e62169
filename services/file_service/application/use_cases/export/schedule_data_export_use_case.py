from datetime import datetime, timezone
from typing import List
from uuid import UUID, uuid4
from zoneinfo import ZoneInfo

from services.base.application.assets import Assets
from services.base.application.async_message_broker_client import AsyncMessageBrokerClient
from services.base.application.async_use_case_base import AsyncUseCaseBase
from services.base.application.database.models.filter_types import TimestampRangeFilter
from services.base.application.event_models.takeout_export_scheduled_event_model import TakeoutExportScheduledModel
from services.base.application.exceptions import ConflictingActionInProgress
from services.base.application.utils.member_user.member_user_settings import get_user_timezone
from services.base.domain.annotated_types import AssetId
from services.base.domain.constants.messaging import ATT_NAME_TAKEOUT_EXPORT_EVENT, MessageTopics
from services.base.domain.enums.metadata import Organization
from services.base.domain.enums.task_status import TaskStatus
from services.base.domain.repository.export_task_repository import ExportTaskRepository
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.schemas.export_task import ExportTask
from services.base.message_queue.utils import create_string_message_attribute
from services.file_service.api.request_models.export_data_request_models import EnvironmentAggregationInterval
from services.file_service.application.enums.exportable_data_type import ExportableType


class ScheduleDataExportUseCase(AsyncUseCaseBase):
    def __init__(
        self,
        message_broker_client: AsyncMessageBrokerClient,
        member_user_settings_repo: MemberUserSettingsRepository,
        export_repo: ExportTaskRepository,
    ):
        self._message_broker_client = message_broker_client
        self._member_user_settings_repo = member_user_settings_repo
        self._export_task_repo = export_repo

    async def execute_async(
        self,
        user_id: UUID,
        data_types: List[ExportableType],
        organizations: List[Organization],
        export_csv: bool,
        environment_aggregation_interval: EnvironmentAggregationInterval,
        system_run: bool,
        range_filter: TimestampRangeFilter | None = None,
        storage_path: str | None = None,
    ) -> UUID:
        now = datetime.now(timezone.utc)
        export_task = await self._export_task_repo.get_user_tasks(user_id=user_id, active_only=True)
        if export_task:
            raise ConflictingActionInProgress("Another export is already running or scheduled.")
        if not storage_path:
            storage_path = Assets.generate_user_storage_container_name(user_uuid=user_id)

        user_timezone = await get_user_timezone(uuid=user_id, member_user_settings_repo=self._member_user_settings_repo)
        takeout_name = Assets.generate_asset_id(name="export.zip")
        export_task = await self._export_task_repo.insert_or_update(
            task=ExportTask(user_id=user_id, status=TaskStatus.SCHEDULED, takeout_name=takeout_name, id=uuid4())
        )

        await self._publish_export_scheduled(
            timestamp=now,
            user_id=user_id,
            task_id=export_task.id,
            takeout_name=takeout_name,
            data_types=data_types,
            user_timezone=user_timezone,
            range_filter=range_filter,
            organizations=organizations,
            export_csv=export_csv,
            environment_aggregation_interval=environment_aggregation_interval,
            system_run=system_run,
            storage_path=storage_path,
        )

        return export_task.id

    async def _publish_export_scheduled(
        self,
        timestamp: datetime,
        user_id: UUID,
        task_id: UUID,
        takeout_name: AssetId,
        data_types: list[ExportableType],
        user_timezone: ZoneInfo,
        organizations: List[Organization],
        export_csv: bool,
        environment_aggregation_interval: EnvironmentAggregationInterval,
        system_run: bool,
        storage_path: str,
        range_filter: TimestampRangeFilter | None = None,
    ):
        await self._message_broker_client.publish_topic(
            topic_name=MessageTopics.TOPIC_TAKEOUT_EXPORT_SCHEDULED.value,
            message_body=TakeoutExportScheduledModel(
                timestamp=timestamp,
                user_uuid=user_id,
                task_id=task_id,
                takeout_name=takeout_name,
                data_types=data_types,
                user_timezone=str(user_timezone),
                range_filter=range_filter,
                organizations=organizations,
                export_csv=export_csv,
                environment_aggregation_interval=environment_aggregation_interval,
                system_run=system_run,
                storage_path=storage_path,
            ).model_dump_json(),
            message_attributes=create_string_message_attribute(
                ATT_NAME_TAKEOUT_EXPORT_EVENT, MessageTopics.TOPIC_TAKEOUT_EXPORT_SCHEDULED.value
            ),
        )
