from services.file_service.application.enums.exportable_data_type import ExportableType
from services.file_service.application.mappings.csv_mappings.environment_mappings import EnvironmentCSVMappings
from services.file_service.application.mappings.csv_mappings.v2_mappings import V2CSVMappings
from services.file_service.application.mappings.csv_mappings.v3_documents.body_metric_collection_mappings import (
    BodyMetricCollectionCSVMappings,
)
from services.file_service.application.mappings.csv_mappings.v3_documents.content_collection_mappings import (
    ContentCollectionCSVMappings,
)
from services.file_service.application.mappings.csv_mappings.v3_documents.exercise_collection_mappings import (
    ExerciseCollectionCSVMappings,
)
from services.file_service.application.mappings.csv_mappings.v3_documents.feeling_collection_mappings import (
    FeelingsCollectionCSVMappings,
)
from services.file_service.application.mappings.csv_mappings.v3_documents.nutrition_collection_mappings import (
    NutritionCollectionCSVMappings,
)
from services.file_service.application.mappings.csv_mappings.v3_documents.v3_non_collection_mappings import (
    V3CSVMappings,
    V3NonEventCSVMappings,
)
from services.file_service.application.use_cases.export.aggregate_air_quality_use_case import (
    AggregateAirQualityUseCase,
)
from services.file_service.application.use_cases.export.aggregate_pollen_use_case import AggregatePollenUseCase
from services.file_service.application.use_cases.export.aggregate_weather_use_case import AggregateWeatherUseCase

EnvironmentDataTypeToUseCaseMapping = {
    ExportableType.Weather: AggregateWeatherUseCase(),
    ExportableType.AirQuality: AggregateAirQualityUseCase(),
    ExportableType.Pollen: AggregatePollenUseCase(),
}

ExportableDataTypeToCsvFieldsMapping = {
    **EnvironmentCSVMappings,
    **V2CSVMappings,
    **V3CSVMappings,
    **ExerciseCollectionCSVMappings,
    **ContentCollectionCSVMappings,
    **BodyMetricCollectionCSVMappings,
    **FeelingsCollectionCSVMappings,
    **NutritionCollectionCSVMappings,
    **V3NonEventCSVMappings,
}
