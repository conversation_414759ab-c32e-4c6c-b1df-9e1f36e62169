import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import AsyncGenerator, Awaitable, Callable, Sequence
from unittest.mock import ANY, AsyncMock
from uuid import UUID

import pytest

from services.base.domain.enums.metadata import Organization
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.schemas.location import Location
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.tests.domain.builders.member_user_builder import MemberUserBuilder
from services.data_service.application.use_cases.by_id.delete_by_id_use_case import DeleteByIdUseCase
from services.data_service.application.use_cases.loading.location.load_location_use_case import LoadLocationUseCase
from services.data_service.application.use_cases.loading.location.models.load_location_input import (
    LoadLocationInput,
    LocationServiceCoordinates,
)
from services.data_service.application.use_cases.loading.location.models.load_location_input_boundary import (
    LoadLocationInputBoundary,
)
from services.data_service.application.use_cases.loading.metadata_input import MetadataInputModel
from services.data_service.application.workflows.forecast_summary_workflow.forecast_summary_workflow import (
    EvaluatedForecastSummary,
    ForecastSummaryWorkflow,
)


@pytest.fixture(scope="function")
async def yield_user(
    member_user_repository: MemberUserRepository,
) -> AsyncGenerator[Callable[[], Awaitable[MemberUser]], None]:
    created_users: list[MemberUser] = []

    async def get_user() -> MemberUser:
        user = (
            MemberUserBuilder()
            .with_last_logged_at(datetime.now(timezone.utc) + timedelta(hours=-12))
            .with_primary_email("<EMAIL>")
            .build()
        )
        logging.info(f"creating user: {user.user_uuid}")
        tmp_user = await member_user_repository.insert_or_update(user)
        if not tmp_user:
            raise ShouldNotReachHereException("Failed to create user")
        created_users.append(tmp_user)
        return tmp_user

    yield get_user

    for user_to_delete in created_users:
        await member_user_repository.delete(user_to_delete)


@pytest.fixture(scope="function")
async def yield_location(
    load_location_use_case: LoadLocationUseCase,
    delete_by_id_use_case: DeleteByIdUseCase,
) -> AsyncGenerator[Callable[[UUID], Awaitable[Sequence[Location]]], None]:
    loaded_location: list[Location] = []
    user_uid: UUID | None = None

    async def create_location(user_id: UUID) -> Sequence[Location]:
        nonlocal user_uid
        user_uid = user_id

        result = await load_location_use_case.execute_async(
            user_uuid=user_id,
            input_data=LoadLocationInputBoundary(
                documents=[
                    LoadLocationInput(
                        timestamp=datetime.now(timezone.utc),
                        data=LocationServiceCoordinates(latitude=40.87, longitude=74.10),
                    )
                ],
                metadata=MetadataInputModel(organization=Organization.LLIF),
            ),
        )
        loaded_location.extend(result)

        # Wait for the search index to update at least once
        await asyncio.sleep(1)
        return result

    yield create_location

    logging.info("deleting location from database")
    if not user_uid:
        raise ShouldNotReachHereException("User id is not set")
    await delete_by_id_use_case.execute_async(
        doc_ids=[loc.doc_id for loc in loaded_location],
        user_uuid=user_uid,
        data_schema=Location,
    )


async def test_forecast_summary_workflow_run_passes(
    forecast_summary_workflow: ForecastSummaryWorkflow,
    yield_user: Callable[[], Awaitable[MemberUser]],
    yield_location: Callable[[UUID], Awaitable[Sequence[Location]]],
) -> None:
    user: MemberUser = await yield_user()
    assert user

    location: Sequence[Location] = await yield_location(user.user_uuid)
    assert location

    forecast_summary_workflow._evaluate_forecast_into_message = AsyncMock()
    forecast_summary_workflow._evaluate_forecast_into_message.return_value = EvaluatedForecastSummary(
        title="Alert",
        body="Alert's body",
        user_id=user.user_uuid,
    )

    # Set the triggering hour to now so that the test always runs in the pipeline
    now = datetime.now(timezone.utc)
    await forecast_summary_workflow.run(local_time_of_triggering=now.time())
    # Assert we are targeting the expected user in the workflow
    forecast_summary_workflow._evaluate_forecast_into_message.assert_awaited_once_with(
        user_id=user.user_uuid, forecast=ANY
    )
