import pytest

from services.base.domain.enums.event_type import EventType
from services.base.domain.schemas.events.activity import ActivityCategory
from services.base.domain.schemas.events.content.audio import AudioCategory
from services.base.domain.schemas.events.exercise.cardio import CardioCategory
from services.base.domain.schemas.events.nutrition.drink import DrinkCategory
from services.base.domain.schemas.events.symptom import SymptomCategory
from services.data_service.application.use_cases.ai.classify_event_category_use_case import (
    ClassifyEventCategoryInputBoundary,
    ClassifyEventCategoryUseCase,
)


@pytest.mark.integration
@pytest.mark.parametrize(
    "query,expected_event_type,expected_category",
    [
        # Drink events
        pytest.param("I had coffee this morning", EventType.Drink, DrinkCategory.COFFEE, id="drink_coffee"),
        pytest.param("I drank some water", EventType.Drink, DrinkCategory.WATER, id="drink_water"),
        pytest.param("I had a beer with dinner", EventType.Drink, DrinkCategory.BEER, id="drink_beer"),
        pytest.param("I had some green tea", EventType.Drink, DrinkCategory.TEA, id="drink_tea"),
        # Symptom events
        pytest.param(
            "I have a headache", EventType.Symptom, SymptomCategory.PAIN_AND_SENSATIONS_HEADACHE, id="symptom_headache"
        ),
        # Exercise events
        pytest.param("I went for a run", EventType.Cardio, CardioCategory.RUN, id="cardio_running"),
        # Content events
        pytest.param("I listened to a podcast", EventType.Audio, AudioCategory.PODCAST, id="audio_podcast"),
        pytest.param("I listened to music", EventType.Audio, AudioCategory.MUSIC, id="audio_music"),
        # Core events for ambiguous cases
        pytest.param("I did something", EventType.Activity, ActivityCategory.OTHER, id="core_general"),
    ],
)
async def test_classify_event_category(
    classify_event_category_use_case: ClassifyEventCategoryUseCase,
    query: str,
    expected_event_type: EventType,
    expected_category: str | DrinkCategory | SymptomCategory | CardioCategory | AudioCategory | ActivityCategory,
):
    # Act
    result = await classify_event_category_use_case.execute(
        input_boundary=ClassifyEventCategoryInputBoundary(query=query)
    )

    # Assert
    assert result.type == expected_event_type
    assert result.category == expected_category


@pytest.mark.integration
@pytest.mark.parametrize(
    "query",
    [
        pytest.param("I did something today", id="ambiguous_general"),
        pytest.param("Something happened", id="ambiguous_unclear"),
    ],
)
async def test_classify_event_category_ambiguous_queries(
    classify_event_category_use_case: ClassifyEventCategoryUseCase, query: str
):
    # Act
    result = await classify_event_category_use_case.execute(
        input_boundary=ClassifyEventCategoryInputBoundary(query=query)
    )

    # Assert
    assert result.type == EventType.Activity
    assert result.category == ActivityCategory.OTHER


@pytest.mark.integration
async def test_classify_event_category_returns_both_type_and_category(
    classify_event_category_use_case: ClassifyEventCategoryUseCase,
):
    # Arrange
    query = "I drank some water"

    # Act
    result = await classify_event_category_use_case.execute(
        input_boundary=ClassifyEventCategoryInputBoundary(query=query)
    )

    # Assert
    assert isinstance(result.type, EventType)
    assert isinstance(result.category, DrinkCategory)
    assert result.category == DrinkCategory.WATER


@pytest.mark.integration
@pytest.mark.parametrize(
    "query,expected_event_type,expected_category",
    [
        pytest.param(
            "I have a severe headache",
            EventType.Symptom,
            SymptomCategory.PAIN_AND_SENSATIONS_HEADACHE,
            id="symptom_headache_specific",
        ),
        pytest.param(
            "My head really hurts",
            EventType.Symptom,
            SymptomCategory.PAIN_AND_SENSATIONS_HEADACHE,
            id="symptom_head_pain",
        ),
        pytest.param("I feel dizzy", EventType.Symptom, SymptomCategory.NEUROLOGICAL_DIZZINESS, id="symptom_dizziness"),
        pytest.param("I'm coughing a lot", EventType.Symptom, SymptomCategory.RESPIRATORY_COUGH, id="symptom_cough"),
        pytest.param(
            "My stomach hurts", EventType.Symptom, SymptomCategory.DIGESTIVE_ABDOMINAL_PAIN, id="symptom_stomach"
        ),
    ],
)
async def test_classify_event_category_symptom_specificity(
    classify_event_category_use_case: ClassifyEventCategoryUseCase,
    query: str,
    expected_event_type: EventType,
    expected_category: SymptomCategory,
):
    # Act
    result = await classify_event_category_use_case.execute(
        input_boundary=ClassifyEventCategoryInputBoundary(query=query)
    )

    # Assert
    assert result.type == expected_event_type
    assert result.category == expected_category
