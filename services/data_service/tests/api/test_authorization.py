from starlette import status

from services.data_service.api.aggregate_endpoints import aggregate_router
from services.data_service.api.assets_endpoints import assets_router
from services.data_service.api.document_endpoints import document_router
from services.data_service.api.event_endpoints import event_router
from services.data_service.api.plan_endpoints import plan_router
from services.data_service.api.template_endpoints import template_router
from services.data_service.api.use_case_endpoints import use_case_router
from services.data_service.tests.api.conftest import get_path_operation
from services.data_service.v02.api.activity_endpoints import activity_router
from services.data_service.v02.api.data_summary_endpoints import summary_router
from services.data_service.v02.api.delete_endpoints import delete_router
from services.data_service.v02.api.diary_endpoints import diary_router
from services.data_service.v02.api.fetch_endpoints import fetch_router
from services.data_service.v02.api.location_endpoints import location_router
from services.data_service.v02.api.measure_endpoints import measure_router
from services.data_service.v02.api.update_endpoints import update_router
from services.data_service.v1.api.data_crud_endpoints import data_crud_router
from services.data_service.v1.api.environment_endpoints import environment_router
from services.data_service.v1.api.extensions.extension_directory_endpoints import extension_directory_router
from services.data_service.v1.api.extensions.extension_providers_endpoints import extension_providers_router
from services.data_service.v1.api.extensions.extension_results_endpoints import extension_router
from services.data_service.v1.api.temp_plan_endpoints import temp_plan_router


def test_data_endpoints_unauthorized(test_client):
    for route in (
        *activity_router.routes,
        *aggregate_router.routes,
        *assets_router.routes,
        *data_crud_router.routes,
        *delete_router.routes,
        *diary_router.routes,
        *document_router.routes,
        *fetch_router.routes,
        *environment_router.routes,
        *event_router.routes,
        *extension_directory_router.routes,
        *extension_providers_router.routes,
        *extension_router.routes,
        *location_router.routes,
        *measure_router.routes,
        *plan_router.routes,
        *summary_router.routes,
        *temp_plan_router.routes,
        *template_router.routes,
        *update_router.routes,
        *use_case_router.routes,
    ):
        for method in route.methods:
            response = get_path_operation(method=method, test_client=test_client)(route.path)
            assert response.status_code == status.HTTP_401_UNAUTHORIZED
