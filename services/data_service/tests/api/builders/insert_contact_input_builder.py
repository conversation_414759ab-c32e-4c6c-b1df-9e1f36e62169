import random
from datetime import date
from typing import Sequence

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.person_relationship import ContactRelationship
from services.base.domain.schemas.contact import ContactAddress
from services.data_service.application.use_cases.contact.models.insert_contact_input_boundary import (
    InsertContactInput,
)


class InsertContactInputBuilder:
    def __init__(self):
        self._last_name: str | None = None
        self._first_name: str | None = None
        self._company: str | None = None
        self._address: ContactAddress | None = None
        self._note: str | None = None
        self._birthday: date | None = None
        self._relationship: ContactRelationship | None = None
        self._tags: Sequence[str] | None = None

    def build(self) -> InsertContactInput:
        return InsertContactInput(
            last_name=self._last_name or PrimitiveTypesGenerator.generate_random_string(max_length=50),
            first_name=self._first_name or PrimitiveTypesGenerator.generate_random_string(max_length=50),
            company=self._company or PrimitiveTypesGenerator.generate_random_string(max_length=100, allow_none=True),
            address=self._address
            or random.choice(
                [
                    None,
                    ContactAddress(
                        street=PrimitiveTypesGenerator.generate_random_string(max_length=100, allow_none=True),
                        zip=PrimitiveTypesGenerator.generate_random_string(max_length=20, allow_none=True),
                        city=PrimitiveTypesGenerator.generate_random_string(max_length=50, allow_none=True),
                        state=PrimitiveTypesGenerator.generate_random_string(max_length=50, allow_none=True),
                        country=PrimitiveTypesGenerator.generate_random_string(max_length=50),
                    ),
                ]
            ),
            note=self._note or PrimitiveTypesGenerator.generate_random_string(max_length=500, allow_none=True),
            birthday=self._birthday or PrimitiveTypesGenerator.generate_random_date(),
            relationship=self._relationship
            or PrimitiveTypesGenerator.generate_random_enum(enum_type=ContactRelationship),
            tags=self._tags or PrimitiveTypesGenerator.generate_random_tags(),
        )

    def with_last_name(self, last_name: str):
        self._last_name = last_name
        return self

    def with_first_name(self, first_name: str):
        self._first_name = first_name
        return self

    def with_company(self, company: str):
        self._company = company
        return self

    def with_birthday(self, birthday: date):
        self._birthday = birthday
        return self

    def with_relationship(self, relationship: ContactRelationship):
        self._relationship = relationship
        return self

    def with_tags(self, tags: Sequence[str]):
        self._tags = tags
        return self
