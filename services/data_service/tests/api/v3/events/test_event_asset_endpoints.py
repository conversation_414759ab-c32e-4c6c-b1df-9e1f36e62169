import json
import random
from typing import Awaitable, Callable, Sequence

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.event_v3_type import EventV3Type
from services.base.domain.enums.metadata_v3 import Origin
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.tests.domain.builders.event_builder import EventBuilder
from services.data_service.api.models.output.events.event_api_output_v3 import EventV3APIOutput
from services.data_service.api.models.request.event.modify_event_assets_api_request_input import (
    ModifyEventAssetsAPIRequestInput,
)
from services.data_service.api.urls import EventEndpointUrls
from services.data_service.application.services.asset_service import AssetService
from services.data_service.application.use_cases.events.models.modify_event_assets_input_boundary import (
    ModifyEventAssetsInputBoundaryItem,
)
from services.data_service.tests.api.common_rpc_calls import (
    _call_patch_endpoint,
)
from services.data_service.tests.application.builders.v3.event_input_asset_builder import EventInputAssetBuilder


class TestEventAssetEndpoints:
    @pytest.fixture
    async def user_with_events_and_assets(
        self,
        event_repo: EventRepository,
        user_factory: Callable[[], Awaitable[MemberUser]],
        asset_service: AssetService,
    ) -> tuple[Sequence[Event], MemberUser]:
        user: MemberUser = await user_factory()

        events_without_assets = (
            EventBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_origin(origin=PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
            .build_n()
        )

        events_with_assets = []
        for n in range(PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=3)):
            input_assets = EventInputAssetBuilder().build_n()
            events_with_assets.append(
                EventBuilder()
                .with_owner_id(owner_id=user.user_uuid)
                .with_origin(origin=PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
                .with_asset_references(
                    asset_references=await asset_service.store_input_assets(
                        owner_id=user.user_uuid, assets=input_assets
                    )
                )
                .build()
            )

        inserted_events = await event_repo.insert(
            events=[*events_with_assets, *events_without_assets], force_strong_consistency=True
        )

        yield inserted_events, user

        # Teardown
        await event_repo.delete_by_id(ids=[e.id for e in inserted_events])

    async def test_modify_assets_endpoint_adds_and_removes_passes(
        self,
        user_with_events_and_assets: tuple[Sequence[Event], MemberUser],
    ):
        # Arrange
        existing_events, user = user_with_events_and_assets
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        modify_inputs = [
            ModifyEventAssetsInputBoundaryItem(
                id=e.id,
                type=EventV3Type(e.type),
                asset_ids_to_remove=(
                    [
                        a.asset_id
                        for a in random.sample(
                            e.asset_references,
                            k=PrimitiveTypesGenerator.generate_random_int(
                                min_value=1, max_value=len(e.asset_references)
                            ),
                        )
                    ]
                    if e.asset_references
                    else None
                ),
                assets_to_add=EventInputAssetBuilder().build_n(n=2),
            )
            for e in existing_events
        ]
        request_input = ModifyEventAssetsAPIRequestInput(items=modify_inputs)

        # Act
        response = await _call_patch_endpoint(
            request_url=EventEndpointUrls.MODIFY_ASSETS,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.json()
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())
        docs = payload.documents
        assert docs

        for modify_input, modified_event in zip(
            sorted(modify_inputs, key=lambda e: e.id), sorted(docs, key=lambda e: e.id)
        ):
            asset_ids = [a.asset_id for a in modified_event.asset_references]
            if modify_input.asset_ids_to_remove:
                assert all([a not in asset_ids for a in modify_input.asset_ids_to_remove])

            asset_names = [id.split("-")[1] for id in asset_ids]
            assert all([a.name in asset_names for a in modify_input.assets_to_add])

    async def test_modify_assets_endpoint_removes_from_empty_raises(
        self,
        user_with_events_and_assets: tuple[Sequence[Event], MemberUser],
    ):
        # Arrange
        existing_events, user = user_with_events_and_assets
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        modify_inputs = [
            ModifyEventAssetsInputBoundaryItem(
                id=e.id,
                type=EventV3Type(e.type_id()),
                asset_ids_to_remove=[PrimitiveTypesGenerator.generate_random_string()],
                assets_to_add=None,
            )
            for e in [e for e in existing_events if not e.asset_references]
        ]
        request_input = ModifyEventAssetsAPIRequestInput(items=modify_inputs)

        # Act
        response = await _call_patch_endpoint(
            request_url=EventEndpointUrls.MODIFY_ASSETS,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, response.json()

    async def test_modify_assets_endpoint_removes_non_existing_raises(
        self,
        user_with_events_and_assets: tuple[Sequence[Event], MemberUser],
    ):
        # Arrange
        existing_events, user = user_with_events_and_assets
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        modify_inputs = [
            ModifyEventAssetsInputBoundaryItem(
                id=e.id,
                type=EventV3Type(e.type_id()),
                asset_ids_to_remove=[PrimitiveTypesGenerator.generate_random_string()],
                assets_to_add=None,
            )
            for e in [e for e in existing_events if e.asset_references]
        ]
        request_input = ModifyEventAssetsAPIRequestInput(items=modify_inputs)

        # Act
        response = await _call_patch_endpoint(
            request_url=EventEndpointUrls.MODIFY_ASSETS,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, response.json()

    async def test_modify_assets_endpoint_empty_input_raises(
        self,
        user_with_events_and_assets: tuple[Sequence[Event], MemberUser],
    ):
        # Arrange
        existing_events, user = user_with_events_and_assets
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        modify_inputs = [
            {
                "id": str(e.id),
                "type": e.type_id(),
                "asset_ids_to_remove": None,
                "assets_to_add": None,
            }
            for e in existing_events
        ]
        request_input = {"items": modify_inputs}

        # Act
        response = await _call_patch_endpoint(
            request_url=EventEndpointUrls.MODIFY_ASSETS,
            headers=headers,
            json=request_input,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY, response.json()

    async def test_modify_assets_endpoint_removes_non_existing_assets_passes(
        self,
        user_with_events_and_assets: tuple[Sequence[Event], MemberUser],
        asset_service: AssetService,
    ):
        """
        A weird case of server inconsistency, that an event would have asset reference, but the asset was removed.
        As that is not a client-side issue, the test is expected to pass and delete the reference.
        """
        # Arrange
        existing_events, user = user_with_events_and_assets
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        e = random.choice([e for e in existing_events if e.asset_references])
        asset_ids = [a.asset_id for a in e.asset_references]
        await asset_service.delete_assets(asset_ids=asset_ids, owner_id=user.user_uuid)

        to_remove = random.choice(asset_ids)
        modify_input = ModifyEventAssetsInputBoundaryItem(
            id=e.id,
            type=EventV3Type(e.type_id()),
            asset_ids_to_remove=[to_remove],
            assets_to_add=None,
        )

        request_input = ModifyEventAssetsAPIRequestInput(items=[modify_input])
        # Act
        response = await _call_patch_endpoint(
            request_url=EventEndpointUrls.MODIFY_ASSETS,
            headers=headers,
            json=json.loads(request_input.model_dump_json(by_alias=True)),
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, response.json()
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())
        docs = payload.documents
        assert docs
        modified_e = docs[0]

        # As we delete, the modified references can be None
        if len(e.asset_references) > 1:
            assert modified_e.asset_references
            asset_ids = [a.asset_id for a in modified_e.asset_references]
            assert all([a not in asset_ids for a in modify_input.asset_ids_to_remove])
