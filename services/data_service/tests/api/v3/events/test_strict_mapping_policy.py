import pytest
from opensearchpy import Async<PERSON><PERSON>Search
from pytest import ExceptionInfo

from services.base.dependency_bootstrapper import DependencyBootstrapper
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.events.event import Event
from services.base.infrastructure.database.opensearch.opensearch_mappings import DataSchemaToIndexModelMapping
from services.base.tests.domain.builders.event_builder import EventBuilder


class TestStrictMappingPolicy:

    @pytest.mark.parametrize("event", EventBuilder().build_all())
    async def test_inserting_value_for_events_v3_which_is_not_in_mapping_should_raise(
        self, dependency_bootstrapper: DependencyBootstrapper, event: Event
    ):
        os_client = dependency_bootstrapper.get(interface=AsyncOpenSearch)
        index_name = DataSchemaToIndexModelMapping[type(event)].name

        event_as_dict = event.model_dump(exclude={DocumentLabels.ID}) | {
            DocumentLabels.TAGS: ([{DocumentLabels.TAG: tag} for tag in event.tags] if event.tags else None)
        }

        extended_dict = event_as_dict | {"random_field": "test"}

        with pytest.raises(Exception) as e:
            await os_client.index(index=index_name, body=extended_dict, id=event.id)
        assert isinstance(e, ExceptionInfo)
        assert e.value.args[1] == "strict_dynamic_mapping_exception"
