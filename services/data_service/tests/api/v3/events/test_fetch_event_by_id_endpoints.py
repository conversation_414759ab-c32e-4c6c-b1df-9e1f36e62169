from typing import AsyncGenerator, Awaitable, Callable, Sequence

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.tests.domain.builders.event_builder import EventBuilder
from services.data_service.api.models.output.events.event_api_output_v3 import EventV3APIOutput
from services.data_service.api.urls import EventEndpointUrls
from services.data_service.tests.api.common_rpc_calls import _call_post_endpoint


class TestFetchEventByIdEndpoints:
    @pytest.fixture
    async def user_with_events(
        self, event_repo: EventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> AsyncGenerator[tuple[Sequence[Event], MemberUser], None]:
        user: MemberUser = await user_factory()
        events = EventBuilder().with_owner_id(owner_id=user.user_uuid).build_all()

        inserted_events = await event_repo.insert(events=events, force_strong_consistency=True)

        yield inserted_events, user

        # Teardown
        await event_repo.delete_by_id(ids=[e.id for e in inserted_events])

    @pytest.fixture
    async def other_user_with_events(
        self, event_repo: EventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> AsyncGenerator[tuple[Sequence[Event], MemberUser], None]:
        user: MemberUser = await user_factory()
        events = EventBuilder().with_owner_id(owner_id=user.user_uuid).build_n(n=2)

        inserted_events = await event_repo.insert(events=events, force_strong_consistency=True)

        yield inserted_events, user

        # Teardown
        await event_repo.delete_by_id(ids=[e.id for e in inserted_events])

    async def test_fetch_events_by_id_single_event_success(
        self,
        user_with_events: tuple[Sequence[Event], MemberUser],
    ):
        # Arrange
        existing_events, user = user_with_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        event_to_fetch = existing_events[0]

        request_data = {"ids": [str(event_to_fetch.id)]}

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BY_ID,
            headers=headers,
            json=request_data,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())
        docs = payload.documents
        assert len(docs) == 1

        returned_event = docs[0]
        assert returned_event.id == event_to_fetch.id
        assert returned_event.type == event_to_fetch.type_id()
        assert returned_event.name == event_to_fetch.name
        assert returned_event.timestamp == event_to_fetch.timestamp

    async def test_fetch_events_by_id_multiple_events_success(
        self,
        user_with_events: tuple[Sequence[Event], MemberUser],
    ):
        # Arrange
        existing_events, user = user_with_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        events_to_fetch = existing_events[:3]  # Fetch first 3 events

        request_data = {"ids": [str(event.id) for event in events_to_fetch]}

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BY_ID,
            headers=headers,
            json=request_data,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"Unexpected response: {response.json()}"
        payload = CommonDocumentsResponse[EventV3APIOutput](**response.json())
        docs = payload.documents
        assert len(docs) == len(events_to_fetch)

        # Verify all requested events are returned
        returned_event_ids = {doc.id for doc in docs}
        expected_event_ids = {event.id for event in events_to_fetch}
        assert returned_event_ids == expected_event_ids

        # Verify each event is correct
        for returned_event in docs:
            matching_event = next(e for e in events_to_fetch if e.id == returned_event.id)
            assert returned_event.type == matching_event.type_id()
            assert returned_event.name == matching_event.name
            assert returned_event.timestamp == matching_event.timestamp

    async def test_fetch_events_by_id_nonexistent_event_fails(
        self,
        user_with_events: tuple[Sequence[Event], MemberUser],
    ):
        # Arrange
        _, user = user_with_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        fake_event_id = PrimitiveTypesGenerator.generate_random_typed_uuid(type=DataType.Content)
        request_data = {"ids": [str(fake_event_id)]}

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BY_ID,
            headers=headers,
            json=request_data,
            retry=False,
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST, f"Unexpected response: {response.json()}"

    async def test_fetch_events_by_id_mixed_existing_and_nonexistent_fails(
        self,
        user_with_events: tuple[Sequence[Event], MemberUser],
    ):
        # Arrange
        existing_events, user = user_with_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        existing_event = existing_events[0]

        fake_event_id = PrimitiveTypesGenerator.generate_random_typed_uuid(type=DataType.Content)
        request_data = {"ids": [str(existing_event.id), str(fake_event_id)]}

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BY_ID,
            headers=headers,
            json=request_data,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, f"Unexpected response: {response.json()}"

    async def test_fetch_events_by_id_wrong_owner_fails(
        self,
        user_with_events: tuple[Sequence[Event], MemberUser],
        other_user_with_events: tuple[Sequence[Event], MemberUser],
    ):
        # Arrange
        user1_events, user1 = user_with_events
        user2_events, user2 = other_user_with_events

        # User1 tries to fetch User2's event
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user1.user_uuid)}"}
        user2_event = user2_events[0]

        request_data = {"ids": [str(user2_event.id)]}

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BY_ID,
            headers=headers,
            json=request_data,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, f"Unexpected response: {response.json()}"

    async def test_fetch_events_by_id_duplicate_documents_fails(
        self,
        user_with_events: tuple[Sequence[Event], MemberUser],
    ):
        # Arrange
        existing_events, user = user_with_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        event_to_fetch = existing_events[0]

        # Request the same event twice
        request_data = {"ids": [str(event_to_fetch.id), str(event_to_fetch.id)]}

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BY_ID,
            headers=headers,
            json=request_data,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY, f"Unexpected response: {response.json()}"
        response_detail = response.json()["detail"]
        assert any("duplicate entries found" in str(error) for error in response_detail)

    async def test_fetch_events_by_id_empty_documents_fails(
        self,
        user_with_events: tuple[Sequence[Event], MemberUser],
    ):
        # Arrange
        existing_events, user = user_with_events
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        request_data = {"ids": []}

        # Act
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BY_ID,
            headers=headers,
            json=request_data,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY, f"Unexpected response: {response.json()}"

    async def test_fetch_events_by_id_unauthorized_fails(
        self,
        user_with_events: tuple[Sequence[Event], MemberUser],
    ):
        # Arrange
        existing_events, user = user_with_events
        event_to_fetch = existing_events[0]

        request_data = {"ids": [str(event_to_fetch.id)]}

        # Act - No authorization header
        response = await _call_post_endpoint(
            request_url=EventEndpointUrls.BY_ID,
            headers={},  # No auth header
            json=request_data,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED, f"Unexpected response: {response.json()}"
