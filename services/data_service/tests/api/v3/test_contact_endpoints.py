import json
from typing import Async<PERSON>enerator, Callable, Sequence

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.api.responses.common_document_responses import CommonDocumentsResponse
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.application.utils.urls import join_as_url
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.repository.contact_repository import ContactRepository
from services.base.domain.schemas.contact import Contact, ContactFields
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.tests.domain.builders.contact_builder import ContactBuilder
from services.data_service.api.models.request.contact.insert_contact_api_request_input import (
    InsertContactAPIRequestInput,
)
from services.data_service.api.models.request.contact.update_contact_api_request_input import (
    UpdateContactAPIRequestInput,
)
from services.data_service.api.models.response.contact.contact_api_output import ContactAPIOutput
from services.data_service.api.urls import ContactEndpointUrls
from services.data_service.application.use_cases.contact.models.insert_contact_input_boundary import (
    InsertContactInput,
)
from services.data_service.application.use_cases.contact.models.update_contact_input_boundary import (
    UpdateContactInput,
)
from services.data_service.tests.api.builders.insert_contact_api_request_input_builder import (
    InsertContactAPIRequestInputBuilder,
)
from services.data_service.tests.api.builders.insert_contact_input_builder import InsertContactInputBuilder
from services.data_service.tests.api.common_rpc_calls import (
    _call_patch_endpoint,
    _call_post_endpoint,
)
from services.data_service.tests.api.utils.test_utils import TestUtils


class TestContactCRUD:
    @pytest.fixture
    async def user_with_contacts(
        self,
        contact_repo: ContactRepository,
        user_factory: Callable[[], MemberUser],
    ) -> AsyncGenerator[tuple[Sequence[Contact], MemberUser]]:
        user: MemberUser = await user_factory()
        contacts = [
            ContactBuilder().with_owner_id(owner_id=user.user_uuid).build()
            for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))
        ]
        inserted_contacts = await contact_repo.insert(contacts=contacts, force_strong_consistency=True)

        yield inserted_contacts, user

        # Teardown
        await contact_repo.delete_by_id(ids=[c.id for c in inserted_contacts])

    async def test_insert_contacts_passes(self, user_with_contacts, contact_repo: ContactRepository):
        # Arrange
        existing_contacts, user = user_with_contacts
        load_contact_request = (
            InsertContactAPIRequestInputBuilder()
            .with_contacts(contacts=[InsertContactInputBuilder().build() for _ in range(1, 10)])
            .build()
        )
        body_dict = json.loads(load_contact_request.model_dump_json(by_alias=True))
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        # Act
        response = await _call_post_endpoint(
            request_url=ContactEndpointUrls.BASE, json=body_dict, headers=headers, retry=False
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response content: {response.content}"
        response_model = CommonDocumentsResponse[ContactAPIOutput](**response.json())
        assert len(response_model.documents) == len(load_contact_request.documents)

        for contact, expected_contact in zip(
            sorted(response_model.documents, key=lambda c: c.last_name),
            sorted(load_contact_request.documents, key=lambda c: c.last_name),
        ):
            assert contact.last_name == expected_contact.last_name
            assert contact.first_name == expected_contact.first_name
            assert contact.tags == expected_contact.tags
            assert TestUtils.is_date_within_one_minute(contact.system_properties.created_at)

        # Teardown
        await contact_repo.delete_by_id([c.id for c in response_model.documents])

    async def test_insert_duplicated_contacts_raises(self, user_with_contacts):
        # Arrange
        existing_contacts, user = user_with_contacts
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        # Act
        response = await _call_post_endpoint(
            request_url=ContactEndpointUrls.BASE,
            json=json.loads(
                InsertContactAPIRequestInput(
                    documents=[InsertContactInput(**c.model_dump()) for c in existing_contacts]
                ).model_dump_json()
            ),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, f"response content: {response.content}"

    async def test_archive_contacts_passes(self, user_with_contacts):
        # Arrange
        existing_contacts, user = user_with_contacts
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        request_url = join_as_url(
            base_url=ContactEndpointUrls.ARCHIVE, query_params={"contact_ids": [c.id for c in existing_contacts]}
        )

        # Act
        response = await _call_patch_endpoint(request_url=request_url, headers=headers, json=None, retry=False)

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response content: {response.content}"
        archived_contacts = CommonDocumentsResponse[ContactAPIOutput](**response.json()).documents
        for contact in archived_contacts:
            archived_at = contact.archived_at
            assert archived_at
            assert TestUtils.is_date_within_one_minute(date=archived_at)

    async def test_search_contacts_endpoint_passes(self, user_with_contacts):
        # Arrange
        existing_contacts, user = user_with_contacts
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        # Act
        response = await _call_post_endpoint(
            request_url=ContactEndpointUrls.SEARCH,
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response content: {response.content}"
        actual_contacts = CommonDocumentsResponse[ContactAPIOutput](**response.json()).documents
        assert len(actual_contacts) == len(existing_contacts)

        expected_contact: Contact
        contact: ContactAPIOutput
        for contact, expected_contact in zip(
            sorted(actual_contacts, key=lambda c: c.id), sorted(existing_contacts, key=lambda c: c.id)
        ):
            assert contact.id == expected_contact.id
            assert contact.last_name == expected_contact.last_name
            assert contact.first_name == expected_contact.first_name
            assert contact.tags == expected_contact.tags
            assert contact.system_properties.created_at == expected_contact.system_properties.created_at

    async def test_update_contacts_by_id_passes(self, user_with_contacts):
        # Arrange
        existing_contacts, user = user_with_contacts
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}
        input_contacts = []

        for contact in existing_contacts:
            input_contact = Contact(
                **contact.model_dump(by_alias=True)
                | {
                    ContactFields.LAST_NAME: PrimitiveTypesGenerator.generate_random_string(),
                    ContactFields.FIRST_NAME: PrimitiveTypesGenerator.generate_random_string(),
                    DocumentLabels.TAGS: [
                        PrimitiveTypesGenerator.generate_random_string()
                        for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=3))
                    ],
                }
            )

            input_contacts.append(input_contact)

        # Act
        response = await _call_patch_endpoint(
            request_url=ContactEndpointUrls.BASE,
            json=json.loads(
                UpdateContactAPIRequestInput(
                    documents=[UpdateContactInput(**c.model_dump()) for c in input_contacts]
                ).model_dump_json()
            ),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response content: {response.content}"
        updated_contacts = CommonDocumentsResponse[ContactAPIOutput](**response.json()).documents

        input_contact: Contact
        updated_contact: ContactAPIOutput
        for input_contact, updated_contact in zip(
            sorted(input_contacts, key=lambda c: c.id), sorted(updated_contacts, key=lambda c: c.id)
        ):
            updated_at = updated_contact.system_properties.updated_at
            assert TestUtils.is_date_within_one_minute(updated_at)
            assert updated_contact.last_name == input_contact.last_name
            assert updated_contact.first_name == input_contact.first_name
            assert updated_contact.tags == input_contact.tags
            assert updated_contact.system_properties.created_at == input_contact.system_properties.created_at

    async def test_update_contacts_by_id_when_duplication_fields_do_not_change_passes(self, user_with_contacts):
        # Arrange
        existing_contacts, user = user_with_contacts
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        input_contacts = []

        for contact in existing_contacts:
            input_contact = Contact(
                **contact.model_dump(by_alias=True)
                | {DocumentLabels.TAGS: PrimitiveTypesGenerator.generate_random_tags()}
            )
            input_contacts.append(input_contact)

        # Act
        response = await _call_patch_endpoint(
            request_url=ContactEndpointUrls.BASE,
            json=json.loads(
                UpdateContactAPIRequestInput(
                    documents=[UpdateContactInput(**c.model_dump()) for c in input_contacts]
                ).model_dump_json()
            ),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_200_OK, f"response content: {response.content}"
        updated_contacts = CommonDocumentsResponse[ContactAPIOutput](**response.json()).documents

        input_contact: Contact
        updated_contact: ContactAPIOutput
        for input_contact, updated_contact in zip(
            sorted(input_contacts, key=lambda c: c.id), sorted(updated_contacts, key=lambda c: c.id)
        ):
            updated_at = updated_contact.system_properties.updated_at
            assert TestUtils.is_date_within_one_minute(updated_at)
            assert updated_contact.last_name == input_contact.last_name
            assert updated_contact.first_name == input_contact.first_name
            assert updated_contact.tags == input_contact.tags
            assert updated_contact.system_properties.created_at == input_contact.system_properties.created_at

    async def test_update_contacts_by_id_when_update_makes_duplicate_raises(
        self, contact_repo: ContactRepository, user_with_contacts
    ):
        # Arrange
        existing_contacts, user = user_with_contacts

        contacts = ContactBuilder().with_owner_id(owner_id=user.user_uuid).build_n(n=2)
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=user.user_uuid)}"}

        inserted_contacts = await contact_repo.insert(contacts=contacts, force_strong_consistency=True)

        # Switch contact id and update content of one to be duplicate of the other
        updated_contact = UpdateContactInput.map(
            model=inserted_contacts[0], fields={DocumentLabels.ID: inserted_contacts[1].id}
        )

        # Act
        response = await _call_patch_endpoint(
            request_url=ContactEndpointUrls.BASE,
            json=json.loads(UpdateContactAPIRequestInput(documents=[updated_contact]).model_dump_json()),
            headers=headers,
            retry=False,
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST, f"response content: {response.content}"
