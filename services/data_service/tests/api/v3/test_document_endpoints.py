import asyncio
from typing import Awaitable, Callable, Sequence
from uuid import UUID

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.domain.enums.event_v3_type import EventV3Type
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.repository.use_case_repository import UseCaseRepository
from services.base.domain.schemas.events.document_base import AssetReference
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.plan import Plan
from services.base.domain.schemas.events.use_case import UseCase
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.base.domain.schemas.shared import BaseDataModel
from services.base.domain.schemas.templates.template import Template
from services.base.tests.domain.builders.event_builder import EventBuilder
from services.base.tests.domain.builders.plan_builder import PlanBuilder
from services.base.tests.domain.builders.template.event_template_builder import EventTemplateBuilder
from services.base.tests.domain.builders.template.group_template_builder import GroupTemplateBuilder
from services.base.tests.domain.builders.use_case_builder import UseCaseBuilder
from services.data_service.api.urls import DocumentEndpointURLs
from services.data_service.application.services.asset_service import AssetService
from services.data_service.application.use_cases.events.models.shared import EventInputAsset
from services.data_service.dependency_bootstrapper import DependencyBootstrapper
from services.data_service.tests.api.common_rpc_calls import _call_delete_endpoint
from services.data_service.tests.application.builders.v3.event_input_asset_builder import EventInputAssetBuilder


class UserWithV3Data(BaseDataModel):
    user_uuid: UUID
    events: Sequence[Event]
    plans: Sequence[Plan]
    templates: Sequence[Template]
    use_cases: Sequence[UseCase]
    asset_references: Sequence[AssetReference]


class TestDocumentEndpoints:
    @pytest.fixture
    async def user_with_v3_data(
        self,
        event_repo: EventRepository,
        template_repo: TemplateRepository,
        plan_repo: PlanRepository,
        use_case_repo: UseCaseRepository,
        asset_service: AssetService,
        user_factory: Callable[[], Awaitable[MemberUser]],
    ):
        user: MemberUser = await user_factory()

        assets: Sequence[EventInputAsset] = EventInputAssetBuilder().build_n()
        asset_references: Sequence[AssetReference] = await asset_service.store_input_assets(
            owner_id=user.user_uuid,
            assets=assets,
            override=True,
        )
        assert await asset_service.does_user_container_exist(owner_id=user.user_uuid)

        events = (
            EventBuilder()
            .with_owner_id(owner_id=user.user_uuid)
            .with_asset_references(asset_references=asset_references)
            .build_all()
        )
        plans = PlanBuilder().with_owner_id(owner_id=user.user_uuid).build_n()
        event_templates = EventTemplateBuilder().with_owner_id(owner_id=user.user_uuid).build_all()
        group_templates = GroupTemplateBuilder().with_owner_id(owner_id=user.user_uuid).build_n()
        templates = list(event_templates) + list(group_templates)
        use_cases = UseCaseBuilder().with_owner_id(owner_id=user.user_uuid).build_n()

        events = await event_repo.insert(events=events, force_strong_consistency=True)
        templates = await template_repo.insert(templates=templates, force_strong_consistency=True)
        plans = await plan_repo.insert(plans=plans, force_strong_consistency=True)
        use_cases = await use_case_repo.insert(use_cases=use_cases, force_strong_consistency=True)

        yield UserWithV3Data(
            user_uuid=user.user_uuid,
            events=events,
            templates=templates,
            plans=plans,
            use_cases=use_cases,
            asset_references=asset_references,
        )

        # Teardown
        await event_repo.delete_by_id(ids=[e.id for e in events])
        await template_repo.delete_by_id(ids=[t.id for t in templates])
        await plan_repo.delete_by_id(ids=[p.id for p in plans])
        await use_case_repo.delete_by_id(ids=[ut.id for ut in use_cases])
        await asset_service.delete_user_assets(user_uuid=user.user_uuid)

    async def test_delete_v3_data_endpoint_deletes_all_data(
        self,
        dependency_bootstrapper: DependencyBootstrapper,
        user_with_v3_data: UserWithV3Data,
    ):
        response = await _call_delete_endpoint(
            request_url=DocumentEndpointURLs.ALL_DATA,
            headers={"Authorization": "Bearer " + generate_access_token(user_uuid=user_with_v3_data.user_uuid)},
            retry=False,
        )

        assert response.status_code == status.HTTP_202_ACCEPTED

        # Prevent race conditions for the usecase to finish executing,
        await asyncio.sleep(3)

        event_repo = dependency_bootstrapper.get(interface=EventRepository)
        template_repo = dependency_bootstrapper.get(interface=TemplateRepository)
        plan_repo = dependency_bootstrapper.get(interface=PlanRepository)
        usecase_repo = dependency_bootstrapper.get(interface=UseCaseRepository)
        asset_service = dependency_bootstrapper.get(interface=AssetService)

        # Assert data is gone
        assert not await event_repo.search_by_id(ids=[e.id for e in user_with_v3_data.events])
        assert not await template_repo.search_by_id(ids=[t.id for t in user_with_v3_data.templates])
        assert not await plan_repo.search_by_id(ids=[t.id for t in user_with_v3_data.plans])
        assert not await usecase_repo.search_by_id(ids=[t.id for t in user_with_v3_data.use_cases])
        assert not await asset_service.does_user_container_exist(owner_id=user_with_v3_data.user_uuid)

    async def test_delete_v3_data_endpoint_deletes_data_by_query(
        self,
        dependency_bootstrapper: DependencyBootstrapper,
        user_with_v3_data: UserWithV3Data,
    ):
        # Get the type of the first event and track name to delete, all other data SHOULD NOT be deleted
        event_to_delete = user_with_v3_data.events[0]

        response = await _call_delete_endpoint(
            request_url=DocumentEndpointURLs.BY_QUERY,
            headers={"Authorization": "Bearer " + generate_access_token(user_uuid=user_with_v3_data.user_uuid)},
            json={
                "queries": [
                    {
                        "types": [event_to_delete.type_id()],
                        "query": {
                            "type": "values",
                            "field_name": "name",
                            "values": [event_to_delete.name],
                        },
                    }
                ]
            },
            retry=False,
        )

        assert response.status_code == status.HTTP_202_ACCEPTED

        # Prevent race conditions for the usecase to finish executing,
        await asyncio.sleep(3)

        event_repo = dependency_bootstrapper.get(interface=EventRepository)

        # Assert deleted event is gone
        result = await event_repo.search_by_single_query(
            query=SingleDocumentTypeQuery(
                domain_type=EventV3Type(event_to_delete.type_id()).to_event_model(),
                query=ValuesQuery(type="values", field_name="name", values=[event_to_delete.name]),
            ),
            size=10,
        )
        assert not result.documents

        # Assert everything else stayed
        returned_events = await event_repo.search_by_id(ids=[e.id for e in user_with_v3_data.events])
        assert returned_events
        assert len(returned_events) == len(user_with_v3_data.events) - 1  # One was deleted
