# -*- coding: utf-8 -*-
# snapshottest: v1 - https://goo.gl/zC4yUc
from __future__ import unicode_literals

from services.data_service.tests.api.snapshot_impl import Snapshot

snapshots = Snapshot()

snapshots["test_body_parts_endpoint_should_pass 1"] = {
    "body": [
        {"code": 101, "name": "right.front.head.forehead"},
        {"code": 102, "name": "left.front.head.forehead"},
        {"code": 103, "name": "right.front.head.face"},
        {"code": 104, "name": "left.front.head.face"},
        {"code": 105, "name": "right.front.neck"},
        {"code": 106, "name": "left.front.neck"},
        {"code": 107, "name": "right.front.torso.shoulder"},
        {"code": 108, "name": "right.front.torso.chest"},
        {"code": 109, "name": "left.front.torso.chest"},
        {"code": 110, "name": "left.front.torso.shoulder"},
        {"code": 111, "name": "right.front.torso.shoulder.arm.upper"},
        {"code": 112, "name": "left.front.torso.shoulder.arm.upper"},
        {"code": 113, "name": "right.front.torso.shoulder.arm.elbow"},
        {"code": 114, "name": "left.front.torso.shoulder.arm.elbow"},
        {"code": 115, "name": "right.front.torso.shoulder.arm.forearm"},
        {"code": 116, "name": "right.front.torso.abdomen"},
        {"code": 117, "name": "left.front.torso.abdomen"},
        {"code": 118, "name": "left.front.torso.shoulder.arm.forearm"},
        {"code": 119, "name": "right.front.torso.shoulder.arm.wrist"},
        {"code": 120, "name": "right.front.pelvis.hip"},
        {"code": 121, "name": "right.front.pelvis.genital"},
        {"code": 122, "name": "left.front.pelvis.genital"},
        {"code": 123, "name": "left.front.pelvis.hip"},
        {"code": 124, "name": "left.front.torso.shoulder.arm.wrist"},
        {"code": 125, "name": "right.front.torso.shoulder.arm.hand"},
        {"code": 126, "name": "right.front.leg.upper"},
        {"code": 127, "name": "left.front.leg.upper"},
        {"code": 128, "name": "left.front.torso.shoulder.arm.hand"},
        {"code": 129, "name": "right.front.leg.knee"},
        {"code": 130, "name": "left.front.leg.knee"},
        {"code": 131, "name": "right.front.leg.lower"},
        {"code": 132, "name": "left.front.leg.lower"},
        {"code": 133, "name": "right.front.leg.ankle"},
        {"code": 134, "name": "left.front.leg.ankle"},
        {"code": 135, "name": "right.front.leg.foot"},
        {"code": 136, "name": "left.front.leg.foot"},
        {"code": 201, "name": "left.back.head.top"},
        {"code": 202, "name": "right.back.head.top"},
        {"code": 203, "name": "left.back.head"},
        {"code": 204, "name": "right.back.head"},
        {"code": 205, "name": "left.back.neck"},
        {"code": 206, "name": "right.back.neck"},
        {"code": 207, "name": "left.back.torso.shoulder"},
        {"code": 208, "name": "left.back.torso.upper"},
        {"code": 209, "name": "right.back.torso.upper"},
        {"code": 210, "name": "right.back.torso.shoulder"},
        {"code": 211, "name": "left.back.torso.shoulder.arm.upper"},
        {"code": 212, "name": "left.back.torso.middle"},
        {"code": 213, "name": "right.back.torso.middle"},
        {"code": 214, "name": "right.back.torso.shoulder.arm.upper"},
        {"code": 215, "name": "left.back.torso.shoulder.arm.elbow"},
        {"code": 216, "name": "right.back.torso.shoulder.arm.elbow"},
        {"code": 217, "name": "left.back.torso.shoulder.arm.forearm"},
        {"code": 218, "name": "left.back.torso.lower"},
        {"code": 219, "name": "right.back.torso.lower"},
        {"code": 220, "name": "right.back.torso.shoulder.arm.forearm"},
        {"code": 221, "name": "left.back.torso.shoulder.arm.wrist"},
        {"code": 222, "name": "left.back.pelvis.hip"},
        {"code": 223, "name": "left.back.pelvis.glute"},
        {"code": 224, "name": "right.back.pelvis.glute"},
        {"code": 225, "name": "right.back.pelvis.hip"},
        {"code": 226, "name": "right.back.torso.shoulder.arm.wrist"},
        {"code": 227, "name": "left.back.torso.shoulder.arm.hand"},
        {"code": 228, "name": "left.back.leg.upper"},
        {"code": 229, "name": "right.back.leg.upper"},
        {"code": 230, "name": "right.back.torso.shoulder.arm.hand"},
        {"code": 231, "name": "left.back.leg.knee"},
        {"code": 232, "name": "right.back.leg.knee"},
        {"code": 233, "name": "left.back.leg.lower"},
        {"code": 234, "name": "right.back.leg.lower"},
        {"code": 235, "name": "left.back.leg.ankle"},
        {"code": 236, "name": "right.back.leg.ankle"},
        {"code": 237, "name": "left.back.leg.foot"},
        {"code": 238, "name": "right.back.leg.foot"},
    ]
}

snapshots["test_data_summary_endpoint_no_organization_filter_should_pass 1"] = {
    "data": [
        {
            "count": 34,
            "data_type": "Steps",
            "earliest": "2019-11-21T00:45:00.000-05:00",
            "latest": "2019-11-22T23:30:00.000-05:00",
            "organization": "google",
        }
    ]
}

snapshots["test_data_summary_endpoint_one_organization_filter_should_pass 1"] = {
    "data": [
        {
            "count": 1,
            "data_type": "HeartRate",
            "earliest": "2022-06-06T14:15:22.000+00:00",
            "latest": "2022-06-06T14:50:22.000+00:00",
            "organization": "llif",
        },
        {
            "count": 2,
            "data_type": "HeartRate",
            "earliest": "2018-04-22T22:43:49.000+00:00",
            "latest": "2018-04-23T04:00:28.000+00:00",
            "organization": "fitbit",
        },
        {"count": 0, "data_type": "RestingHeartRate", "earliest": None, "latest": None, "organization": "llif"},
        {
            "count": 23,
            "data_type": "RestingHeartRate",
            "earliest": "2020-11-26T00:00:00.000+00:00",
            "latest": "2020-12-18T00:00:00.000+00:00",
            "organization": "fitbit",
        },
    ]
}

snapshots["test_data_summary_endpoint_several_organizations_filters_should_pass 1"] = {
    "data": [
        {"count": 0, "data_type": "Steps", "earliest": None, "latest": None, "organization": "apple"},
        {"count": 0, "data_type": "Steps", "earliest": None, "latest": None, "organization": "facebook"},
        {"count": 0, "data_type": "Steps", "earliest": None, "latest": None, "organization": "garmin"},
        {
            "count": 34,
            "data_type": "Steps",
            "earliest": "2019-11-21T00:45:00.000-05:00",
            "latest": "2019-11-22T23:30:00.000-05:00",
            "organization": "google",
        },
    ]
}

snapshots["test_data_summary_endpoint_should_pass 1"] = {
    "data": [
        {
            "count": 34,
            "data_type": "Steps",
            "earliest": "2019-11-21T00:45:00.000-05:00",
            "latest": "2019-11-22T23:30:00.000-05:00",
            "organization": "google",
        }
    ]
}

snapshots["test_statistics_endpoint_returns_expected_value_sleep 1"] = {
    "re_fetch_time_input": None,
    "statistics": [
        {
            "aggregation_name": "light_seconds_extended_stats",
            "extended_stats": {
                "avg": 13234.285714285714,
                "count": 7,
                "max": 15900.0,
                "min": 9420.0,
                "std_deviation": 2198.1996343947167,
                "std_deviation_bounds": {
                    "lower": 8837.88644549628,
                    "lower_population": 8837.88644549628,
                    "lower_sampling": 8485.631769670006,
                    "upper": 17630.684983075145,
                    "upper_population": 17630.684983075145,
                    "upper_sampling": 17982.93965890142,
                },
                "std_deviation_population": 2198.1996343947167,
                "std_deviation_sampling": 2374.3269723078533,
                "sum": 92640.0,
                "sum_of_squares": 1259848800.0,
                "variance": 4832081.632653066,
                "variance_population": 4832081.632653066,
                "variance_sampling": 5637428.571428577,
            },
        },
        {
            "aggregation_name": "deep_seconds_extended_stats",
            "extended_stats": {
                "avg": 2400.0,
                "count": 7,
                "max": 3240.0,
                "min": 1440.0,
                "std_deviation": 619.4006319476457,
                "std_deviation_bounds": {
                    "lower": 1161.1987361047086,
                    "lower_population": 1161.1987361047086,
                    "lower_sampling": 1061.9417053057816,
                    "upper": 3638.8012638952914,
                    "upper_population": 3638.8012638952914,
                    "upper_sampling": 3738.0582946942186,
                },
                "std_deviation_population": 619.4006319476457,
                "std_deviation_sampling": 669.0291473471092,
                "sum": 16800.0,
                "sum_of_squares": 43005600.0,
                "variance": 383657.14285714284,
                "variance_population": 383657.14285714284,
                "variance_sampling": 447600.0,
            },
        },
        {
            "aggregation_name": "in_bed_seconds_extended_stats",
            "extended_stats": {
                "avg": 19230.0,
                "count": 8,
                "max": 26160.0,
                "min": 3600.0,
                "std_deviation": 6523.396354660661,
                "std_deviation_bounds": {
                    "lower": 6183.207290678678,
                    "lower_population": 6183.207290678678,
                    "lower_sampling": 5282.391910131277,
                    "upper": 32276.792709321322,
                    "upper_population": 32276.792709321322,
                    "upper_sampling": 33177.60808986872,
                },
                "std_deviation_population": 6523.396354660661,
                "std_deviation_sampling": 6973.804044934362,
                "sum": 153840.0,
                "sum_of_squares": 3298780800.0,
                "variance": 42554700.0,
                "variance_population": 42554700.0,
                "variance_sampling": 48633942.85714286,
            },
        },
        {
            "aggregation_name": "awake_seconds_extended_stats",
            "extended_stats": {
                "avg": 2497.5,
                "count": 8,
                "max": 4620.0,
                "min": 420.0,
                "std_deviation": 1126.0744868790873,
                "std_deviation_bounds": {
                    "lower": 245.35102624182537,
                    "lower_population": 245.35102624182537,
                    "lower_sampling": 89.85147320639271,
                    "upper": 4749.648973758174,
                    "upper_population": 4749.648973758174,
                    "upper_sampling": 4905.148526793608,
                },
                "std_deviation_population": 1126.0744868790873,
                "std_deviation_sampling": 1203.8242633968036,
                "sum": 19980.0,
                "sum_of_squares": 60044400.0,
                "variance": 1268043.75,
                "variance_population": 1268043.75,
                "variance_sampling": 1449192.857142857,
            },
        },
        {
            "aggregation_name": "fall_asleep_seconds_extended_stats",
            "extended_stats": {
                "avg": 0.0,
                "count": 8,
                "max": 0.0,
                "min": 0.0,
                "std_deviation": 0.0,
                "std_deviation_bounds": {
                    "lower": 0.0,
                    "lower_population": 0.0,
                    "lower_sampling": 0.0,
                    "upper": 0.0,
                    "upper_population": 0.0,
                    "upper_sampling": 0.0,
                },
                "std_deviation_population": 0.0,
                "std_deviation_sampling": 0.0,
                "sum": 0.0,
                "sum_of_squares": 0.0,
                "variance": 0.0,
                "variance_population": 0.0,
                "variance_sampling": 0.0,
            },
        },
        {
            "aggregation_name": "efficiency_extended_stats",
            "extended_stats": {
                "avg": 95.25,
                "count": 8,
                "max": 98.0,
                "min": 88.0,
                "std_deviation": 3.152380053229623,
                "std_deviation_bounds": {
                    "lower": 88.94523989354076,
                    "lower_population": 88.94523989354076,
                    "lower_sampling": 88.50992793595117,
                    "upper": 101.55476010645924,
                    "upper_population": 101.55476010645924,
                    "upper_sampling": 101.99007206404883,
                },
                "std_deviation_population": 3.152380053229623,
                "std_deviation_sampling": 3.370036032024414,
                "sum": 762.0,
                "sum_of_squares": 72660.0,
                "variance": 9.9375,
                "variance_population": 9.9375,
                "variance_sampling": 11.357142857142858,
            },
        },
        {
            "aggregation_name": "rem_seconds_extended_stats",
            "extended_stats": {
                "avg": 3034.285714285714,
                "count": 7,
                "max": 4320.0,
                "min": 1260.0,
                "std_deviation": 996.7211552006355,
                "std_deviation_bounds": {
                    "lower": 1040.8434038844432,
                    "lower_population": 1040.8434038844432,
                    "lower_sampling": 881.1219291280954,
                    "upper": 5027.728024686985,
                    "upper_population": 5027.728024686985,
                    "upper_sampling": 5187.4494994433335,
                },
                "std_deviation_population": 996.7211552006355,
                "std_deviation_sampling": 1076.5818925788094,
                "sum": 21240.0,
                "sum_of_squares": 71402400.0,
                "variance": 993453.0612244894,
                "variance_population": 993453.0612244894,
                "variance_sampling": 1159028.571428571,
            },
        },
        {
            "aggregation_name": "after_wakeup_seconds_extended_stats",
            "extended_stats": {
                "avg": 90.0,
                "count": 8,
                "max": 420.0,
                "min": 0.0,
                "std_deviation": 146.9693845669907,
                "std_deviation_bounds": {
                    "lower": -203.9387691339814,
                    "lower_population": -203.9387691339814,
                    "lower_sampling": -224.23376193982904,
                    "upper": 383.9387691339814,
                    "upper_population": 383.9387691339814,
                    "upper_sampling": 404.23376193982904,
                },
                "std_deviation_population": 146.9693845669907,
                "std_deviation_sampling": 157.11688096991452,
                "sum": 720.0,
                "sum_of_squares": 237600.0,
                "variance": 21600.0,
                "variance_population": 21600.0,
                "variance_sampling": 24685.714285714286,
            },
        },
        {
            "aggregation_name": "events_count_extended_stats",
            "extended_stats": {
                "avg": 28.25,
                "count": 8,
                "max": 35.0,
                "min": 9.0,
                "std_deviation": 7.838207703295441,
                "std_deviation_bounds": {
                    "lower": 12.573584593409118,
                    "lower_population": 12.573584593409118,
                    "lower_sampling": 11.491206998797828,
                    "upper": 43.926415406590884,
                    "upper_population": 43.926415406590884,
                    "upper_sampling": 45.00879300120217,
                },
                "std_deviation_population": 7.838207703295441,
                "std_deviation_sampling": 8.379396500601086,
                "sum": 226.0,
                "sum_of_squares": 6876.0,
                "variance": 61.4375,
                "variance_population": 61.4375,
                "variance_sampling": 70.21428571428571,
            },
        },
        {
            "aggregation_name": "asleep_seconds_extended_stats",
            "extended_stats": {
                "avg": 16725.0,
                "count": 8,
                "max": 21540.0,
                "min": 3120.0,
                "std_deviation": 5590.936862458741,
                "std_deviation_bounds": {
                    "lower": 5543.126275082517,
                    "lower_population": 5543.126275082517,
                    "lower_sampling": 4771.074165482585,
                    "upper": 27906.87372491748,
                    "upper_population": 27906.87372491748,
                    "upper_sampling": 28678.925834517417,
                },
                "std_deviation_population": 5590.936862458741,
                "std_deviation_sampling": 5976.962917258707,
                "sum": 133800.0,
                "sum_of_squares": 2487873600.0,
                "variance": 31258575.0,
                "variance_population": 31258575.0,
                "variance_sampling": 35724085.71428572,
            },
        },
    ],
}

snapshots["test_statistics_endpoint_returns_expected_value_steps 1"] = {
    "re_fetch_time_input": None,
    "statistics": [
        {
            "aggregation_name": "steps_extended_stats",
            "extended_stats": {
                "avg": 591.1176470588235,
                "count": 34,
                "max": 1980.0,
                "min": 16.0,
                "std_deviation": 427.37473326082085,
                "std_deviation_bounds": {
                    "lower": -263.63181946281816,
                    "lower_population": -263.63181946281816,
                    "lower_sampling": -276.48591618824025,
                    "upper": 1445.8671135804652,
                    "upper_population": 1445.8671135804652,
                    "upper_sampling": 1458.7212103058873,
                },
                "std_deviation_population": 427.37473326082085,
                "std_deviation_sampling": 433.8017816235319,
                "sum": 20098.0,
                "sum_of_squares": 18090354.0,
                "variance": 182649.16262975777,
                "variance_population": 182649.16262975777,
                "variance_sampling": 188183.98573975044,
            },
        }
    ],
}

snapshots["test_statistics_endpoint_returns_expected_value_weight 1"] = {
    "re_fetch_time_input": None,
    "statistics": [
        {
            "aggregation_name": "body_fat_extended_stats",
            "extended_stats": {
                "avg": 12.672558152398398,
                "count": 86,
                "max": 25.5,
                "min": 2.5,
                "std_deviation": 3.7929219081915466,
                "std_deviation_bounds": {
                    "lower": 5.086714336015305,
                    "lower_population": 5.086714336015305,
                    "lower_sampling": 5.042222201916074,
                    "upper": 20.25840196878149,
                    "upper_population": 20.25840196878149,
                    "upper_sampling": 20.30289410288072,
                },
                "std_deviation_population": 3.7929219081915466,
                "std_deviation_sampling": 3.8151679752411622,
                "sum": 1089.8400011062622,
                "sum_of_squares": 15048.27885857003,
                "variance": 14.386256601639404,
                "variance_population": 14.386256601639404,
                "variance_sampling": 14.55550667930575,
            },
        },
        {
            "aggregation_name": "weight_extended_stats",
            "extended_stats": {
                "avg": 304.87724251582705,
                "count": 87,
                "max": 363.10101318359375,
                "min": 40.0,
                "std_deviation": 102.56435796804448,
                "std_deviation_bounds": {
                    "lower": 99.7485265797381,
                    "lower_population": 99.7485265797381,
                    "lower_sampling": 98.55936464123056,
                    "upper": 510.005958451916,
                    "upper_population": 510.005958451916,
                    "upper_sampling": 511.19512039042354,
                },
                "std_deviation_population": 102.56435796804448,
                "std_deviation_sampling": 103.15893893729825,
                "sum": 26524.320098876953,
                "sum_of_squares": 9001853.506062288,
                "variance": 10519.447525397169,
                "variance_population": 10519.447525397169,
                "variance_sampling": 10641.766682669228,
            },
        },
    ],
}

snapshots["test_tag_count_endpoint_returns_expected_value 1"] = {
    "tags": [
        {"count": 5, "tag_name": "hello darkness"},
        {"count": 5, "tag_name": "test"},
        {"count": 5, "tag_name": "world"},
    ]
}
