from __future__ import annotations

from datetime import datetime, timedelta
from typing import Self, Sequence

from services.base.application.builders.input_asset_builder import InputAssetBuilder
from services.base.application.builders.input_time_interval_builder import InputTimeIntervalBuilder
from services.base.application.generators.custom_models_generators import CustomModelsGenerator
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.application.input_validators.shared import InputAssetModel
from services.base.domain.enums.diary_events.diary_events_type import DiaryEventType
from services.base.domain.schemas.diary_events import (
    DiaryEventsConsumablesExtension,
    DiaryEventsCustomDataItem,
    DiaryEventsPlanExtension,
)
from services.data_service.application.use_cases.loading.diary_event.models.load_diary_events_input import (
    LoadDiaryEventsInput,
)


class LoadDiaryEventsInputBuilder:
    def __init__(self):
        super().__init__()
        self._type: DiaryEventType | None = None
        self._plan: DiaryEventsPlanExtension | None = None
        self._consumable: DiaryEventsConsumablesExtension | None = None
        self._custom_data: list[DiaryEventsCustomDataItem] | None = None
        self._assets: list[InputAssetModel] | None = None
        self._timestamp: datetime | None = None
        self._end_time: datetime | None = None

    def build(self) -> LoadDiaryEventsInput:
        time_interval = CustomModelsGenerator.generate_random_time_interval(
            timestamp=self._timestamp, end_time=self._end_time
        )

        return LoadDiaryEventsInput(
            timestamp=time_interval.timestamp,
            end_time=time_interval.end_time,
            explanation=PrimitiveTypesGenerator.generate_random_string(),
            tags=[
                PrimitiveTypesGenerator.generate_random_string(max_length=30)
                for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=10))
            ],
            type=self._type or PrimitiveTypesGenerator.generate_random_enum(enum_type=DiaryEventType),
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            custom_data=[
                DiaryEventsCustomDataItem(
                    key=PrimitiveTypesGenerator.generate_random_string(),
                    value=PrimitiveTypesGenerator.generate_random_float(),
                )
                for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=10))
            ],
            consumables_extension=self._consumable or None,
            assets=InputAssetBuilder().build_n(),
        )

    def build_series(
        self, n: int | None = None, max_time_delta: timedelta | None = None
    ) -> Sequence[LoadDiaryEventsInput]:
        time_intervals = InputTimeIntervalBuilder().build_series(n=n, max_time_delta=max_time_delta)
        return [
            self.with_timestamp(timestamp=t.timestamp).with_end_time(end_time=t.end_time).build()
            for t in time_intervals
        ]

    def with_type(self, type: DiaryEventType) -> LoadDiaryEventsInputBuilder:
        self._type = type
        return self

    def with_plan(self, plan: DiaryEventsPlanExtension) -> LoadDiaryEventsInputBuilder:
        self._plan = plan
        return self

    def with_timestamp(self, timestamp: datetime) -> Self:
        self._timestamp = timestamp
        return self

    def with_end_time(self, end_time: datetime | None) -> Self:
        self._end_time = end_time
        return self

    def with_custom_data(self, custom_data: list[DiaryEventsCustomDataItem]) -> LoadDiaryEventsInputBuilder:
        self._custom_data = custom_data
        return self

    def with_consumable(self, consumable: DiaryEventsConsumablesExtension) -> LoadDiaryEventsInputBuilder:
        self._consumable = consumable
        return self
