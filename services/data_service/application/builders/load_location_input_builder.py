from datetime import datetime, timedelta
from typing import Self, Sequence

from services.base.application.builders.input_time_interval_builder import InputTimeIntervalBuilder
from services.base.application.generators.custom_models_generators import CustomModelsGenerator
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.activity_type import ActivityType
from services.data_service.application.use_cases.loading.location.models.load_location_input import (
    LoadLocationInput,
    LocationActivity,
    LocationServiceCoordinates,
    Place,
)


class LoadLocationInputBuilder:

    def __init__(self):
        self._timestamp: datetime | None = None
        self._end_time: datetime | None = None

    def build(self) -> LoadLocationInput:
        time_interval = CustomModelsGenerator.generate_random_time_interval(
            timestamp=self._timestamp, end_time=self._end_time
        )
        coordinates = CustomModelsGenerator.generate_random_coordinates()

        place = Place(
            latitude=coordinates.latitude,
            longitude=coordinates.longitude,
            name=PrimitiveTypesGenerator.generate_random_string(),
            address=PrimitiveTypesGenerator.generate_random_string(),
            confidence=PrimitiveTypesGenerator.generate_random_int(),
            timestamp=PrimitiveTypesGenerator.generate_random_aware_datetime(),
        )
        activity = LocationActivity(
            activity_type=PrimitiveTypesGenerator.generate_random_enum(enum_type=ActivityType),
            activity_type_probability=PrimitiveTypesGenerator.generate_random_float(min_value=0, max_value=1),
        )
        data = LocationServiceCoordinates(
            longitude=coordinates.longitude,
            latitude=coordinates.latitude,
            speed=PrimitiveTypesGenerator.generate_random_int(),
            accuracy=PrimitiveTypesGenerator.generate_random_int(),
            altitude=PrimitiveTypesGenerator.generate_random_int(),
            altitudeAccuracy=PrimitiveTypesGenerator.generate_random_int(),
            heading=PrimitiveTypesGenerator.generate_random_int(),
        )
        return LoadLocationInput(
            timestamp=self._timestamp or time_interval.timestamp,
            data=data,
            end_time=self._end_time or time_interval.end_time,
            place=place,
            activity=activity,
        )

    def with_timestamp(self, timestamp: datetime) -> Self:
        self._timestamp = timestamp
        return self

    def with_end_time(self, end_time: datetime | None) -> Self:
        self._end_time = end_time
        return self

    def build_series(
        self, n: int | None = None, max_time_delta: timedelta | None = None
    ) -> Sequence[LoadLocationInput]:
        time_intervals = InputTimeIntervalBuilder().build_series(n=n, max_time_delta=max_time_delta)
        return [
            self.with_timestamp(timestamp=t.timestamp).with_end_time(end_time=t.end_time).build()
            for t in time_intervals
        ]
