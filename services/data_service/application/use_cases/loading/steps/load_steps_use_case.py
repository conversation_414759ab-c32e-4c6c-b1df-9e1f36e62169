from datetime import timedelta
from typing import Iterable, Sequence
from uuid import UUID

from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.utils.metadata import create_metadata
from services.base.domain.constants.time_constants import SECONDS_IN_HOUR
from services.base.domain.schemas.steps import Steps
from services.data_service.application.use_cases.loading.data_bucketing import DataAndTimeBucket
from services.data_service.application.use_cases.loading.steps.models.load_steps_input import LoadStepsInput
from services.data_service.application.use_cases.loading.steps.models.load_steps_input_boundary import (
    LoadStepsInputBoundary,
)
from services.data_service.application.use_cases.loading.steps.steps_mapper import StepsMapper
from services.data_service.application.use_cases.loading.time_splitter import TimeSplitter


class LoadStepsUseCase:
    def __init__(self, event_repo: DeprEventRepository):
        self._event_repo = event_repo

    async def execute_async(self, input_data: LoadStepsInputBoundary, user_uuid: UUID) -> Sequence[Steps]:
        buckets: Iterable[DataAndTimeBucket[LoadStepsInput]] = TimeSplitter.split(
            input_data=input_data.documents, load_aggregation_interval=timedelta(seconds=SECONDS_IN_HOUR)
        )

        metadata = create_metadata(user_uuid=user_uuid, **input_data.metadata.model_dump(by_alias=True))
        return await self._event_repo.insert(
            models=[StepsMapper.map_value(input_data=item, metadata=metadata) for item in buckets],
        )
