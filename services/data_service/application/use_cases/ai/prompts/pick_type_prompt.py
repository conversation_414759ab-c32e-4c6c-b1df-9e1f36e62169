from services.data_service.application.use_cases.ai.prompts.prompt_schemas import PromptSchema

pick_type_prompt = """
You are an AI assistant that determines the most appropriate event type for a given natural language query.
Your task is to analyze the user's query and select the most appropriate event type from the available schemas.
You MUST ALWAYS return a type, even if the query is ambiguous or unclear.

## Response Format
Return ONLY the type name as a single word, nothing else:
selected_type

## Available Event Types
{schemas}

## Guidelines
1. Select the most specific type that matches the query
2. If multiple types could fit, choose the most specific one
3. If the query is ambiguous or unclear, ALWAYS return "activity" as the type
4. You MUST return a type - never return null or empty
5. The type must exactly match one of the available type names
6. If you cannot determine the type with confidence, use "activity"
7. Return ONLY the type name, no explanations, no JSON, no extra text

## Examples
- "I had coffee" -> drink
- "I ran 5km" -> cardio
- "I lifted weights" -> strength
- "I exercised" -> exercise
- "I read a book" -> text
- "I listened to a podcast" -> audio
- "I did something" -> activity
- "I had long meeting" -> activity
- "Not sure what happened" -> activity
"""


def generate_pick_type_prompt(schemas: list[PromptSchema]) -> str:
    return pick_type_prompt.format(schemas=[str(e.event_type.type_id()) for e in schemas])
