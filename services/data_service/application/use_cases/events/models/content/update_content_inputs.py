from abc import ABC
from typing import Literal

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.content.audio import AudioCategory, AudioIdentifier
from services.base.domain.schemas.events.content.content import ContentCategory, ContentIdentifier
from services.base.domain.schemas.events.content.content_collection import ContentFields, ContentValueLimits
from services.base.domain.schemas.events.content.image import ImageCategory, ImageIdentifier
from services.base.domain.schemas.events.content.interactive import InteractiveCategory, InteractiveIdentifier
from services.base.domain.schemas.events.content.text import TextCategory, TextIdentifier
from services.base.domain.schemas.events.content.video import VideoCategory, VideoIdentifier
from services.data_service.application.use_cases.events.models.update_event_input import UpdateEventInput


class UpdateContentInputBase(UpdateEventInput, ABC):
    title: NonEmptyStr | None = Field(alias=ContentFields.TITLE, max_length=ContentValueLimits.TITLE_MAX_LENGTH)
    url: str | None = Field(alias=ContentFields.URL, max_length=ContentValueLimits.URL_MAX_LENGTH)
    rating: int | None = Field(
        alias=ContentFields.RATING,
        ge=ContentValueLimits.CONTENT_RATING_MINIMUM_VALUE,
        le=ContentValueLimits.CONTENT_RATING_MAXIMUM_VALUE,
    )
    note: NonEmptyStr | None = Field(
        alias=ContentFields.NOTE,
    )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.url,
                self.title,
            ]
        )


class UpdateAudioInput(UpdateContentInputBase, AudioIdentifier):
    type: Literal[DataType.Audio] = Field(alias=ContentFields.TYPE)
    category: AudioCategory = Field(alias=ContentFields.CATEGORY)


class UpdateContentInput(UpdateContentInputBase, ContentIdentifier):
    type: Literal[DataType.Content] = Field(alias=ContentFields.TYPE)
    category: ContentCategory = Field(alias=ContentFields.CATEGORY)


class UpdateImageInput(UpdateContentInputBase, ImageIdentifier):
    type: Literal[DataType.Image] = Field(alias=ContentFields.TYPE)
    category: ImageCategory = Field(alias=ContentFields.CATEGORY)


class UpdateInteractiveInput(UpdateContentInputBase, InteractiveIdentifier):
    type: Literal[DataType.Interactive] = Field(alias=ContentFields.TYPE)
    category: InteractiveCategory = Field(alias=ContentFields.CATEGORY)


class UpdateTextInput(UpdateContentInputBase, TextIdentifier):
    type: Literal[DataType.Text] = Field(alias=ContentFields.TYPE)
    category: TextCategory = Field(alias=ContentFields.CATEGORY)


class UpdateVideoInput(UpdateContentInputBase, VideoIdentifier):
    type: Literal[DataType.Video] = Field(alias=ContentFields.TYPE)
    category: VideoCategory = Field(alias=ContentFields.CATEGORY)
