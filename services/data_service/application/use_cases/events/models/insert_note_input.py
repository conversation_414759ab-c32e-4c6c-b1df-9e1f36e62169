from typing import Literal
from uuid import uuid4

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.events.event import EventFields, EventValueLimits
from services.base.domain.schemas.events.note import Note, NoteCategory, NoteFields, NoteIdentifier
from services.data_service.application.use_cases.events.models.insert_event_input import (
    EventInsertionContext,
    InsertEventInput,
)


class InsertNoteInput(InsertEventInput, NoteIdentifier):
    type: Literal[DataType.Note] = Field(alias=EventFields.TYPE)
    category: NoteCategory = Field(alias=NoteFields.CATEGORY)
    note: NonEmptyStr = Field(alias=NoteFields.NOTE, max_length=EventValueLimits.MAX_NOTE_LENGTH, min_length=1)

    def to_domain(self, ctx: EventInsertionContext) -> Note:
        return Note(
            # technical
            type=DataType.Note,
            rbac=RBACSchema(owner_id=ctx.owner_id),
            group_id=ctx.group_id,
            submission_id=ctx.submission_id,
            template_id=self.template_id,
            id=uuid4(),
            category=self.category,
            plan_extension=self.plan_extension,
            # common
            timestamp=self.timestamp,
            end_time=self.end_time,
            metadata=ctx.metadata,
            tags=self.tags,
            asset_references=ctx.asset_references,
            name=self.name,
            note=self.note,
        )

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.note or "",
            ]
        )
