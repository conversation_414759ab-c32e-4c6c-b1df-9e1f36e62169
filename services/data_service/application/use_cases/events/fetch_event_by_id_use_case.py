from typing import Sequence
from uuid import UUID

from services.base.application.exceptions import (
    IncorrectOperationException,
    InvalidPrivilegesException,
)
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.events.event import Event
from services.data_service.application.use_cases.events.models.fetch_event_by_id_input_boundary import (
    FetchEventByIdInputBoundary,
)


class FetchEventByIdUseCase:
    def __init__(self, event_repository: EventRepository):
        self._event_repository = event_repository

    async def execute_async(self, boundary: FetchEventByIdInputBoundary, owner_id: UUID) -> Sequence[Event]:
        existing_events: Sequence[Event] = await self._event_repository.search_by_id(
            ids=boundary.ids,
        )

        if len(boundary.ids) != len(existing_events):
            e_ids = [e.id for e in existing_events]
            not_found_ids = [str(p) for p in boundary.ids if p not in e_ids]
            raise IncorrectOperationException(message=f"Documents {not_found_ids} were not found")

        for event in existing_events:
            if event.rbac.owner_id != owner_id:
                raise InvalidPrivilegesException(message="You don't have permission to access some of the documents")

        return existing_events
