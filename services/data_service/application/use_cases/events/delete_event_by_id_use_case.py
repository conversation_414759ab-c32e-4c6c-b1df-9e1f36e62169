import logging
from typing import Sequence
from uuid import UUID

from services.base.application.database.models.sorts import CommonSorts, SortOrder
from services.base.application.exceptions import (
    IncorrectOperationException,
    InvalidPrivilegesException,
)
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.events import EventGroups
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.data_service.application.services.asset_service import AssetService
from services.data_service.application.use_cases.events.models.delete_event_input_boundary import (
    DeleteEventInputBoundary,
)


class DeleteEventByIdUseCase:
    def __init__(self, event_repository: EventRepository, asset_service: AssetService):
        self._event_repository = event_repository
        self._asset_service = asset_service

    async def execute_async(self, boundary: DeleteEventInputBoundary, owner_id: UUID) -> Sequence[UUID]:
        events_to_be_deleted = await self._fetch_events_from_db(input_ids=boundary.ids, owner_id=owner_id)

        await self._asset_service.delete_assets(
            owner_id=owner_id,
            asset_ids=[a.asset_id for e in events_to_be_deleted if e.asset_references for a in e.asset_references],
        )
        # We are creating set to remove duplications. Duplication can happen when user sends to delete group and an
        # event which is also part of the group (which is being deleted). Check the test `test_delete_group_and_its_events_should_return_correct_number_of_ids`
        ids = set([e.id for e in events_to_be_deleted])
        return await self._event_repository.delete_by_id(ids=list(ids))

    async def _fetch_events_from_db(self, input_ids: Sequence[UUID], owner_id: UUID) -> Sequence[Event]:
        existing_events: Sequence[Event] = await self._event_repository.search_by_id(ids=input_ids)

        if len(input_ids) != len(existing_events):
            e_ids = [e.id for e in existing_events]
            not_found_ids = [str(p) for p in input_ids if p not in e_ids]
            raise IncorrectOperationException(message=f"Documents {not_found_ids} were not found")

        for event in existing_events:
            if event.rbac.owner_id != owner_id:
                raise InvalidPrivilegesException(message="You don't have permission to delete some of the documents")

        groups = [e for e in existing_events if isinstance(e, EventGroups)]

        grouped_events = await self._fetch_event_linked_to_groups(groups=groups, owner_id=owner_id)
        return [*existing_events, *grouped_events]

    async def _fetch_event_linked_to_groups(self, groups: Sequence[EventGroups], owner_id: UUID) -> Sequence[Event]:
        if not groups:
            return []

        sorts = [CommonSorts.timestamp(order=SortOrder.DESCENDING)]
        group_id_query = ValuesQuery(field_name=DocumentLabels.GROUP_ID, values=[str(g.id) for g in groups])
        single_query = SingleDocumentTypeQuery[Event](query=group_id_query, domain_type=Event)
        search_res = await self._event_repository.search_by_single_query(sorts=sorts, size=1000, query=single_query)
        grouped_events = search_res.documents
        for event in grouped_events:
            if event.rbac.owner_id != owner_id:
                logging.error(f"group {event.group_id} has different owner than linked event {event.id}")
                raise InvalidPrivilegesException(message="You don't have permission to delete some of the documents")

        nested_groups = [event for event in grouped_events if isinstance(event, EventGroups)]
        nested_group_events = []

        if nested_groups:
            nested_group_events = await self._fetch_event_linked_to_groups(nested_groups, owner_id)

        return [*grouped_events, *nested_group_events]
