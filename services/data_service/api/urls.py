from dataclasses import dataclass

from services.data_service.api.aggregate_endpoints import aggregate_router
from services.data_service.api.ai_endpoints import ai_router
from services.data_service.api.analyse_endpoints import analyse_router
from services.data_service.api.assets_endpoints import assets_router
from services.data_service.api.constants import (
    AggregationEndpointRoutes,
    AIEndpointRoutes,
    AnalyseEndpointRoutes,
    AssetEndpointRoutes,
    ContactEndpointRoutes,
    DocumentEndpointRoutes,
    EventEndpointRoutes,
    LookupEndpointRoutes,
    PlanEndpointRoutes,
    RecordEndpointRoutes,
    TemplateRoutes,
    UseCaseEndpointRoutes,
)
from services.data_service.api.contact_endpoints import contact_router
from services.data_service.api.document_endpoints import document_router
from services.data_service.api.event_endpoints import event_router
from services.data_service.api.lookup_endpoints import lookup_router
from services.data_service.api.plan_endpoints import plan_router
from services.data_service.api.record_endpoints import record_router
from services.data_service.api.template_endpoints import template_router
from services.data_service.api.use_case_endpoints import use_case_router
from services.data_service.v1.api.temp_plan_endpoints import temp_plan_router


@dataclass(frozen=True)
class AssetsEndpointUrls:
    BY_ID = f"{assets_router.prefix}{AssetEndpointRoutes.BY_ID}"
    URL = f"{assets_router.prefix}{AssetEndpointRoutes.URL}"


@dataclass(frozen=True)
class PlanEndpointUrls:
    BASE = f"{plan_router.prefix}{PlanEndpointRoutes.BASE}"
    SEARCH = f"{plan_router.prefix}{PlanEndpointRoutes.SEARCH}"
    ARCHIVE = f"{plan_router.prefix}{PlanEndpointRoutes.ARCHIVE}"
    COMPLETE = f"{plan_router.prefix}{PlanEndpointRoutes.COMPLETE}"
    TEMP = f"{temp_plan_router.prefix}{PlanEndpointRoutes.TEMP}"
    TEMP_ARCHIVE = f"{temp_plan_router.prefix}{PlanEndpointRoutes.TEMP_ARCHIVE}"


@dataclass(frozen=True)
class TemplateEndpointUrls:
    BASE = f"{template_router.prefix}{TemplateRoutes.BASE}"
    SEARCH = f"{template_router.prefix}{TemplateRoutes.SEARCH}"
    ARCHIVE = f"{template_router.prefix}{TemplateRoutes.ARCHIVE}"


class UseCaseEndpointUrls:
    BASE = f"{use_case_router.prefix}{UseCaseEndpointRoutes.BASE}"
    SEARCH = f"{use_case_router.prefix}{UseCaseEndpointRoutes.SEARCH}"
    ARCHIVE = f"{use_case_router.prefix}{UseCaseEndpointRoutes.ARCHIVE}"


@dataclass(frozen=True)
class ContactEndpointUrls:
    BASE = f"{contact_router.prefix}{ContactEndpointRoutes.BASE}"
    SEARCH = f"{contact_router.prefix}{ContactEndpointRoutes.SEARCH}"
    ARCHIVE = f"{contact_router.prefix}{ContactEndpointRoutes.ARCHIVE}"


@dataclass(frozen=True)
class EventEndpointUrls:
    BASE = f"{event_router.prefix}{EventEndpointRoutes.BASE}"
    FEED = f"{event_router.prefix}{EventEndpointRoutes.FEED}"
    MODIFY_ASSETS = f"{event_router.prefix}{EventEndpointRoutes.MODIFY_ASSETS}"
    BY_ID = f"{event_router.prefix}{EventEndpointRoutes.BY_ID}"


@dataclass(frozen=True)
class RecordEndpointUrls:
    BASE = f"{record_router.prefix}{RecordEndpointRoutes.BASE}"


@dataclass(frozen=True)
class AggregationEndpointUrls:
    FREQUENCY_DISTRIBUTION = f"{aggregate_router.prefix}{AggregationEndpointRoutes.FREQUENCY_DISTRIBUTION}"
    DATE_HISTOGRAM = f"{aggregate_router.prefix}{AggregationEndpointRoutes.DATE_HISTOGRAM}"
    CALENDAR_FREQUENCY_DISTRIBUTION = (
        f"{aggregate_router.prefix}{AggregationEndpointRoutes.CALENDAR_FREQUENCY_DISTRIBUTION}"
    )
    CALENDAR_HISTOGRAM_AGGREGATION = (
        f"{aggregate_router.prefix}{AggregationEndpointRoutes.CALENDAR_HISTOGRAM_AGGREGATION}"
    )


@dataclass(frozen=True)
class AnalyseEndpointUrls:
    TREND_DETECT = f"{analyse_router.prefix}{AnalyseEndpointRoutes.TREND_DETECT}"
    CORRELATE_EVENT = f"{analyse_router.prefix}{AnalyseEndpointRoutes.CORRELATE_EVENT}"
    CORRELATE_EVENT_SUGGEST_PARAMETERS = (
        f"{analyse_router.prefix}{AnalyseEndpointRoutes.CORRELATE_EVENT_SUGGEST_PARAMETERS}"
    )


@dataclass(frozen=True)
class LookupEndpointURLs:
    BASE = lookup_router.prefix + LookupEndpointRoutes.BASE


@dataclass(frozen=True)
class DocumentEndpointURLs:
    BASE = document_router.prefix + DocumentEndpointRoutes.BASE
    BY_QUERY = document_router.prefix + DocumentEndpointRoutes.BY_QUERY
    ALL_DATA = document_router.prefix + DocumentEndpointRoutes.ALL_DATA


@dataclass(frozen=True)
class AIEndpointUrls:
    SUGGEST_EVENT = f"{ai_router.prefix}{AIEndpointRoutes.SUGGEST_EVENT}"
