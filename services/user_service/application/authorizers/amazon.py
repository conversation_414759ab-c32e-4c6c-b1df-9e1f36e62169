import json
import logging
from typing import Optional
from uuid import UUID

from httpx import AsyncClient, Response

from services.base.application.constants import OAuth2Keys
from services.base.application.exceptions import RuntimeException
from services.base.application.utils.urls import join_as_url
from services.base.domain.enums.client_apps import ClientApps
from services.base.domain.enums.provider import SupportedApiProviders
from services.base.domain.repository.member_user_oauth2_repository import MemberUserOAuth2Repository
from services.base.domain.schemas.shared import BaseDataModel
from services.user_service.application.boundaries.auth_by_code_input import AuthByCodeInput
from services.user_service.application.use_cases.auth_use_cases.api_auth import ProviderOAuth2ApiAuthorizer
from services.user_service.application.use_cases.auth_use_cases.providers.amazon.sign_in_utils import AmazonOAuth2Utils
from services.user_service.application.use_cases.auth_use_cases.utils import (
    get_common_oauth2_authorize_parameters,
    get_oauth2_authorize_url,
)


class AmazonTokenResponse(BaseDataModel):
    access_token: str
    refresh_token: str
    token_type: str
    expires_in: int


class AmazonOAuth2Authorizer(ProviderOAuth2ApiAuthorizer):
    _scopes = "profile:user_id"

    def build_api_auth_url(
        self, redirect_uri: str, scope: str, client: ClientApps, provider: SupportedApiProviders, state: str
    ) -> str:
        params = get_common_oauth2_authorize_parameters(
            redirect_uri=redirect_uri, scope=scope or self.scopes, client=client, provider=provider.value, state=state
        )

        return join_as_url(
            base_url=get_oauth2_authorize_url(client=client, provider=provider.value), query_params=params
        )

    async def update_oauth_access_data(
        self,
        response_body: AuthByCodeInput,
        member_user_oauth2_repository: MemberUserOAuth2Repository,
        user_uuid: UUID,
        provider: SupportedApiProviders,
    ) -> str:
        try:
            # get oauth2 params and token url for provider as Tuple
            params, token_url = super().prepare_token_endpoint_parameters(
                code=response_body.code,
                redirect_uri=response_body.redirect_uri,
                client=response_body.client,
                provider=provider,
                state=response_body.state,
            )

            raw_token_response = await self.exchange_auth_code(
                params=params,
                token_url=token_url,
            )

            token_response_json: str = raw_token_response.json()
            token_response: AmazonTokenResponse = AmazonTokenResponse(**dict(token_response_json))

            amazon_profile_response = dict(
                (await AmazonOAuth2Utils.get_amazon_profile_response(access_token=token_response.access_token)).json()
            )
            amazon_profile_response[OAuth2Keys.ACCESS_TOKEN.value] = token_response.access_token

            amazon_id = str(amazon_profile_response["user_id"])

            await AmazonOAuth2Utils.update_user_in_database(
                amazon_user_id=amazon_id,
                user_uuid=user_uuid,
                user_data=json.dumps(amazon_profile_response),
                member_user_oauth2_repository=member_user_oauth2_repository,
                provider_refresh_token=token_response.refresh_token,
            )

            return token_response.access_token

        except Exception as error:
            logging.exception("Unable to process Amazon Oauth2 response:\n%s, exception: %s", response_body, error)
            raise RuntimeException(message="Unable to process Oauth2 Amazon response.")

    async def renew_access_token(self, refresh_token: str, user_uuid: UUID, oauth2_repo: MemberUserOAuth2Repository):
        pass

    async def exchange_auth_code(self, params: dict, token_url: str, headers: Optional[dict] = None):
        async with AsyncClient() as client:
            auth_response: Response = await client.post(
                token_url,
                json=params,
                headers=headers,
            )
            logging.info(f"RESPONSE: {auth_response.json()}")
            auth_response.raise_for_status()
            return auth_response
