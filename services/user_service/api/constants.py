from dataclasses import dataclass

ERROR_SETTINGS_OS_NOTIFICATION = "Logged in successfully, but there was an error setting up device os notification."
PUSH_NOTIFICATIONS_LINKED = "Device was successfully registered for push notifications."
PUSH_NOTIFICATIONS_UNLINKED = "Device was successfully unlinked from push notifications."
PUSH_NOTIFICATIONS_LINK_FAILED = "Device push notifications registration failed."
PUSH_NOTIFICATIONS_UNLINK_FAILED = "Device push notifications registration failed."


@dataclass(frozen=True)
class UserServicePrefixes:
    VERSION1_PREFIX = "/api/v1.0"
    VERSION2_PREFIX = "/api/v0.2"
    USER_PREFIX = "/user"


@dataclass(frozen=True)
class V01UserEndpointRoutes:
    DELETE_USER = "/delete_user/"
    SAVE_SETTINGS = "/settings/save/"
    GET_LOGIN_PROVIDERS = "/get_login_providers/"
    LINK_PUSH_NOTIFICATIONS = "/link_push_notifications/"
    UNLINK_PUSH_NOTIFICATIONS = "/unlink_push_notifications/"
    UNLINK_LOGIN_PROVIDER = "/unlink_login_provider/"
    SIGN_OUT_FROM_ALL = "/sign_out_from_all/"
    UPLOAD_STATES = "/upload_states/"
    PROFILE = "/profile/"
    LOGS = "/logs/"


@dataclass(frozen=True)
class UserEndpointRoutes:
    ASSETS_CREDENTIALS = "/asset_credentials/"
    EXPORT_TASKS = "/export_tasks/"


@dataclass(frozen=True)
class V02SignInEndpointRoutes:
    BY_CODE = "/register_or_login_user_by_code/"
    WITH_APPLE_CALLBACK = "/sign_in_with_apple_callback/"
    WITH_APPLE_MOBILE = "/sign_in_with_apple_mobile/"
    ANONYMOUSLY = "/sign_in_anonymous_user/"
    LOGOUT = "/logout/"
    REFRESH = "/refresh/"
    GENERATE_LOCAL_ACCESS = "/generate_local_access_token/"
    SIGN_IN_URL = "/get_login_auth_url/"
    SIGN_IN_PARAMETERS = "/get_login_auth_parameters/"


@dataclass(frozen=True)
class SignInEndpointRoutes:
    SIGN_IN_URL = "/get_sign_in_url/"
    SIGN_IN_PARAMETERS = "/get_sign_in_parameters/"


class NotificationInboxEndpointRoutes:
    INBOX = "/inbox/"
    SET_STATUS = f"{INBOX}set_status/"


@dataclass(frozen=True)
class InboxMessageEndpointRoutes:
    INBOX = "/inbox/"
    SEARCH = INBOX + "search/"
